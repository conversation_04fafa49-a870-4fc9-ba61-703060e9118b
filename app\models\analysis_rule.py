"""
分析规则数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime,
    ForeignKey, DECIMAL, JSON
)
from sqlalchemy.orm import relationship
from .base import BaseModel


class AnalysisRule(BaseModel):
    """分析规则模型"""
    
    __tablename__ = "analysis_rules"
    
    # 关联信息
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=True, index=True, comment="所属门店ID")
    camera_id = Column(Integer, ForeignKey("cameras.id"), nullable=True, index=True, comment="所属摄像头ID")
    
    # 规则基本信息
    rule_name = Column(String(100), nullable=False, index=True, comment="规则名称")
    rule_code = Column(String(50), nullable=False, index=True, comment="规则编码")
    description = Column(Text, comment="规则描述")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 分析配置
    confidence_threshold = Column(DECIMAL(3, 2), default=0.7, comment="置信度阈值")
    continuous_frames = Column(Integer, default=3, comment="连续帧数要求")
    time_window = Column(Integer, default=30, comment="时间窗口(秒)")
    cooldown_period = Column(Integer, default=60, comment="冷却期(秒)")
    
    # 触发条件
    trigger_config = Column(JSON, comment="触发条件配置")
    
    # 时间配置
    start_time = Column(String(8), comment="生效开始时间(HH:MM:SS)")
    end_time = Column(String(8), comment="生效结束时间(HH:MM:SS)")
    effective_days = Column(String(20), comment="生效日期(1-7代表周一到周日)")
    
    # 关联关系
    store = relationship("Store", back_populates="analysis_rules")
    camera = relationship("Camera", back_populates="analysis_rules")
    behavior_mappings = relationship("RuleBehaviorMapping", back_populates="analysis_rule", cascade="all, delete-orphan")
    alert_events = relationship("AlertEvent", back_populates="analysis_rule")
    
    def __repr__(self) -> str:
        return f"<AnalysisRule(id={self.id}, name={self.rule_name}, code={self.rule_code})>"
    
    @property
    def is_global_rule(self) -> bool:
        """是否为全局规则"""
        return self.store_id is None and self.camera_id is None
    
    @property
    def is_store_rule(self) -> bool:
        """是否为门店规则"""
        return self.store_id is not None and self.camera_id is None
    
    @property
    def is_camera_rule(self) -> bool:
        """是否为摄像头专属规则"""
        return self.camera_id is not None
    
    @property
    def scope_description(self) -> str:
        """获取作用域描述"""
        if self.is_camera_rule:
            return f"摄像头专属: {self.camera.camera_name}"
        elif self.is_store_rule:
            return f"门店级别: {self.store.store_name}"
        else:
            return "全局默认"
    
    @property
    def effective_days_list(self) -> list:
        """获取生效日期列表"""
        if not self.effective_days:
            return list(range(1, 8))  # 默认全周
        return [int(day.strip()) for day in self.effective_days.split(",") if day.strip().isdigit()]
    
    def get_trigger_config(self, key: str, default=None):
        """获取触发配置"""
        if not self.trigger_config:
            return default
        return self.trigger_config.get(key, default)
    
    def is_time_effective(self, current_time: str, current_weekday: int) -> bool:
        """检查当前时间是否在生效时间内"""
        # 检查日期
        if current_weekday not in self.effective_days_list:
            return False
        
        # 检查时间
        if self.start_time and self.end_time:
            return self.start_time <= current_time <= self.end_time
        
        return True 