"""
数据模型初始化
"""
from .base import Base, BaseModel

# 导入所有模型以确保它们被注册
from .store import Store
from .camera import Camera
from .anomaly_behavior import AnomalyBehavior
from .analysis_rule import AnalysisRule
from .rule_behavior_mapping import RuleBehaviorMapping
from .alert_event import AlertEvent
from .evidence_file import EvidenceFile
from .prompt_template import PromptTemplate
from .api_key import ApiKey

__all__ = [
    "Base",
    "BaseModel",
    "Store",
    "Camera", 
    "AnomalyBehavior",
    "AnalysisRule",
    "RuleBehaviorMapping",
    "AlertEvent",
    "EvidenceFile",
    "PromptTemplate",
    "ApiKey"
] 