"""
数据库引擎管理
"""
import logging
from typing import Optional
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine
from sqlalchemy.pool import QueuePool

from app.core.config import settings

logger = logging.getLogger(__name__)

# 全局数据库引擎
_engine: Optional[AsyncEngine] = None


def create_engine() -> AsyncEngine:
    """创建异步数据库引擎"""
    global _engine
    
    if _engine is None:
        logger.info("创建数据库引擎...")
        
        _engine = create_async_engine(
            settings.database.url,
            poolclass=QueuePool,
            pool_size=settings.database.pool_size,
            max_overflow=settings.database.max_overflow,
            pool_timeout=settings.database.pool_timeout,
            pool_recycle=settings.database.pool_recycle,
            pool_pre_ping=True,  # 连接前验证
            echo=settings.is_development,  # 开发环境打印SQL
            echo_pool=settings.is_development,  # 开发环境打印连接池信息
        )
        
        logger.info("✅ 数据库引擎创建成功")
    
    return _engine


def get_engine() -> AsyncEngine:
    """获取数据库引擎"""
    if _engine is None:
        return create_engine()
    return _engine


async def close_engine():
    """关闭数据库引擎"""
    global _engine
    
    if _engine is not None:
        logger.info("关闭数据库引擎...")
        await _engine.dispose()
        _engine = None
        logger.info("✅ 数据库引擎已关闭") 