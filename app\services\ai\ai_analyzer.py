"""
AI分析器 - 基于通义千问的多模态视频分析
"""
import asyncio
import logging
import time
import json
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

import cv2
import numpy as np
import httpx
from PIL import Image
import base64
from io import BytesIO

from app.core.config import settings
from app.core.exceptions import AIAnalysisException
from app.repositories.analysis_rule_repository import AnalysisRuleRepository
from app.repositories.anomaly_behavior_repository import AnomalyBehaviorRepository
from app.repositories.prompt_template_repository import PromptTemplateRepository
from app.repositories.rule_behavior_mapping_repository import RuleBehaviorMappingRepository

logger = logging.getLogger(__name__)


@dataclass
class AnalysisFrame:
    """分析帧数据结构"""
    image: np.ndarray
    timestamp: datetime
    frame_index: int
    camera_id: int
    motion_score: float = 0.0


@dataclass 
class AnalysisResult:
    """分析结果数据结构"""
    camera_id: int
    timestamp: datetime
    detected_behaviors: List[Dict[str, Any]]
    confidence_scores: Dict[str, float]
    description: str
    raw_response: Dict[str, Any]
    processing_time: float
    # 新增：原始帧数据用于保存证据文件
    source_frames: Optional[List[AnalysisFrame]] = None


class QwenVLClient:
    """通义千问VL客户端"""
    
    def __init__(self):
        self.api_key = settings.ai.qwen_api_key
        self.base_url = settings.ai.qwen_base_url
        self.model = settings.ai.qwen_model
        self.timeout = settings.ai.timeout
        self.max_retries = settings.ai.max_retries
        
        # HTTP客户端配置
        self.client = httpx.AsyncClient(
            timeout=self.timeout,
            limits=httpx.Limits(
                max_keepalive_connections=20,
                max_connections=100
            )
        )
    
    async def analyze_frames(
        self, 
        frames: List[AnalysisFrame], 
        prompt: str,
        system_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """分析视频帧"""
        try:
            # 编码图片为base64
            encoded_images = []
            for frame in frames:
                # 转换为PIL图片
                pil_image = Image.fromarray(cv2.cvtColor(frame.image, cv2.COLOR_BGR2RGB))
                
                # 压缩图片以减少API调用成本
                pil_image.thumbnail((1024, 1024), Image.Resampling.LANCZOS)
                
                # 转换为base64
                buffer = BytesIO()
                pil_image.save(buffer, format="JPEG", quality=85)
                img_base64 = base64.b64encode(buffer.getvalue()).decode()
                encoded_images.append(f"data:image/jpeg;base64,{img_base64}")
            
            # 构建请求内容
            content = []
            
            # 添加文本提示
            content.append({
                "type": "text",
                "text": prompt
            })
            
            # 添加图片
            for i, img_data in enumerate(encoded_images):
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": img_data
                    }
                })
            
            # 构建消息
            messages = []
            if system_prompt:
                messages.append({
                    "role": "system",
                    "content": system_prompt
                })
            
            messages.append({
                "role": "user", 
                "content": content
            })
            
            # 构建请求数据
            request_data = {
                "model": self.model,
                "messages": messages,
                "temperature": 0.1,  # 降低随机性，提高一致性
                "max_tokens": 1000,
                "stream": False
            }
            
            # 发送请求
            response = await self._make_request(request_data)
            
            return response
            
        except Exception as e:
            logger.error(f"AI分析失败: {str(e)}")
            raise AIAnalysisException(f"AI分析失败: {str(e)}")
    
    async def _make_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送API请求"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        url = f"{self.base_url}/chat/completions"
        
        for attempt in range(self.max_retries):
            try:
                response = await self.client.post(
                    url,
                    json=request_data,
                    headers=headers
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:
                    # 速率限制，等待后重试
                    wait_time = 2 ** attempt
                    logger.warning(f"API速率限制，等待{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    logger.error(f"API请求失败: {response.status_code} - {response.text}")
                    response.raise_for_status()
                    
            except httpx.TimeoutException:
                logger.warning(f"API请求超时，第{attempt + 1}次重试...")
                if attempt == self.max_retries - 1:
                    raise AIAnalysisException("API请求超时")
                await asyncio.sleep(1)
                continue
            except Exception as e:
                logger.error(f"API请求异常: {str(e)}")
                if attempt == self.max_retries - 1:
                    raise AIAnalysisException(f"API请求失败: {str(e)}")
                await asyncio.sleep(1)
                continue
        
        raise AIAnalysisException("API请求失败，已达到最大重试次数")
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


class SmartAIAnalyzer:
    """智能AI分析器 - 配置驱动的异常行为检测"""
    
    def __init__(self):
        self.qwen_client = QwenVLClient()
        
        # Repository依赖
        self.analysis_rule_repo = AnalysisRuleRepository()
        self.behavior_repo = AnomalyBehaviorRepository()
        self.prompt_repo = PromptTemplateRepository()
        self.rule_behavior_repo = RuleBehaviorMappingRepository()
        
        # 分析缓存
        self._analysis_cache = {}
        self._config_cache = {}
        self._cache_ttl = 300  # 5分钟缓存
    
    async def analyze_camera_frames(
        self, 
        camera_id: int, 
        frames: List[AnalysisFrame]
    ) -> AnalysisResult:
        """分析摄像头帧数据"""
        start_time = time.time()
        
        try:
            # 获取摄像头分析配置
            config = await self._get_camera_analysis_config(camera_id)
            
            if not config or not config.get("enabled", False):
                logger.debug(f"摄像头 {camera_id} 未启用AI分析")
                return self._create_empty_result(camera_id, frames[0].timestamp, start_time)
            
            # 构建分析提示词
            prompt = await self._build_analysis_prompt(camera_id, config)
            system_prompt = config.get("system_prompt")
            
            # 调用AI分析
            logger.debug(f"开始分析摄像头 {camera_id} 的 {len(frames)} 帧数据")
            ai_response = await self.qwen_client.analyze_frames(frames, prompt, system_prompt)
            
            # 解析分析结果
            result = await self._parse_analysis_result(
                camera_id, 
                frames[0].timestamp,
                ai_response, 
                config,
                start_time,
                frames  # 传递原始帧数据
            )
            
            logger.info(
                f"完成摄像头 {camera_id} 分析，"
                f"检测到 {len(result.detected_behaviors)} 个异常行为，"
                f"耗时 {result.processing_time:.2f}s"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"摄像头 {camera_id} 分析失败: {str(e)}")
            raise AIAnalysisException(f"AI分析失败: {str(e)}")
    
    async def _get_camera_analysis_config(self, camera_id: int) -> Dict[str, Any]:
        """获取摄像头分析配置"""
        cache_key = f"camera_config_{camera_id}"
        
        # 检查缓存
        if cache_key in self._config_cache:
            cached_data = self._config_cache[cache_key]
            if time.time() - cached_data["timestamp"] < self._cache_ttl:
                return cached_data["config"]
        
        try:
            # 获取摄像头规则配置
            rules = await self.analysis_rule_repo.get_by_camera_id(camera_id)
            
            if not rules:
                return {"enabled": False}
            
            # 选择最高优先级的规则
            active_rule = max(rules, key=lambda r: r.priority)
            
            if not active_rule.is_enabled:
                return {"enabled": False}
            
            # 获取规则关联的异常行为
            behavior_mappings = await self.rule_behavior_repo.get_by_rule_id(active_rule.id)
            enabled_behaviors = [m for m in behavior_mappings if m.is_enabled]
            
            if not enabled_behaviors:
                return {"enabled": False}
            
            # 获取异常行为详情
            behavior_configs = []
            for mapping in enabled_behaviors:
                behavior = await self.behavior_repo.get_by_id(mapping.behavior_id)
                if behavior and behavior.is_enabled:
                    behavior_configs.append({
                        "behavior": behavior,
                        "mapping": mapping
                    })
            
            if not behavior_configs:
                return {"enabled": False}
            
            # 构建配置
            config = {
                "enabled": True,
                "rule": active_rule,
                "behaviors": behavior_configs,
                "confidence_threshold": active_rule.confidence_threshold,
                "consecutive_frames": active_rule.consecutive_frames,
                "time_window_seconds": active_rule.time_window_seconds,
                "cooldown_seconds": active_rule.cooldown_seconds,
                "system_prompt": "你是一个专业的视频监控AI分析师，请仔细观察视频内容并准确识别异常行为。"
            }
            
            # 缓存配置
            self._config_cache[cache_key] = {
                "config": config,
                "timestamp": time.time()
            }
            
            return config
            
        except Exception as e:
            logger.error(f"获取摄像头 {camera_id} 配置失败: {str(e)}")
            return {"enabled": False}
    
    async def _build_analysis_prompt(self, camera_id: int, config: Dict[str, Any]) -> str:
        """构建分析提示词"""
        try:
            behaviors = config.get("behaviors", [])
            
            if not behaviors:
                return "请分析这些视频帧，描述看到的内容。"
            
            # 获取提示词模板
            prompt_parts = []
            
            # 基础指令
            prompt_parts.append("请仔细分析这些视频帧，检测以下异常行为：")
            
            # 添加每个行为的检测要求
            for i, behavior_config in enumerate(behaviors, 1):
                behavior = behavior_config["behavior"]
                mapping = behavior_config["mapping"]
                
                # 获取专用提示词模板
                template = await self._get_behavior_prompt_template(behavior.id)
                
                if template:
                    behavior_prompt = template.template_content
                else:
                    # 使用默认提示词
                    behavior_prompt = f"""
检测行为: {behavior.name}
描述: {behavior.description}
关键词: {', '.join(behavior.ai_keywords) if behavior.ai_keywords else '无'}
置信度要求: {mapping.confidence_threshold_override or behavior.default_confidence_threshold}
"""
                
                prompt_parts.append(f"{i}. {behavior_prompt}")
            
            # 输出格式要求
            prompt_parts.append("""

请按照以下JSON格式返回分析结果：
{
    "detected_behaviors": [
        {
            "behavior_name": "行为名称",
            "behavior_code": "行为代码", 
            "confidence": 0.85,
            "description": "具体描述",
            "location": "位置信息（如果能识别）"
        }
    ],
    "scene_description": "整体场景描述",
    "risk_level": "low|medium|high|critical"
}

如果没有检测到异常行为，detected_behaviors数组应为空。
""")
            
            return "\n".join(prompt_parts)
            
        except Exception as e:
            logger.error(f"构建提示词失败: {str(e)}")
            return "请分析这些视频帧，检测任何异常行为。"
    
    async def _get_behavior_prompt_template(self, behavior_id: int) -> Optional[Any]:
        """获取行为专用提示词模板"""
        try:
            templates = await self.prompt_repo.get_by_behavior_id(behavior_id)
            if templates:
                # 选择最高性能评分的模板
                return max(templates, key=lambda t: t.performance_score or 0.0)
            return None
        except Exception as e:
            logger.error(f"获取行为 {behavior_id} 提示词模板失败: {str(e)}")
            return None
    
    async def _parse_analysis_result(
        self,
        camera_id: int,
        timestamp: datetime,
        ai_response: Dict[str, Any],
        config: Dict[str, Any],
        start_time: float,
        source_frames: Optional[List[AnalysisFrame]] = None
    ) -> AnalysisResult:
        """解析AI分析结果"""
        processing_time = time.time() - start_time
        
        try:
            # 提取AI响应内容
            content = ai_response.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            # 尝试解析JSON响应
            try:
                if content.startswith("```json"):
                    content = content.split("```json")[1].split("```")[0]
                elif content.startswith("```"):
                    content = content.split("```")[1]
                
                parsed_result = json.loads(content.strip())
            except json.JSONDecodeError:
                logger.warning(f"AI响应不是标准JSON格式，使用文本解析: {content}")
                parsed_result = self._fallback_parse_response(content)
            
            # 提取检测到的异常行为
            detected_behaviors = []
            raw_behaviors = parsed_result.get("detected_behaviors", [])
            
            for behavior in raw_behaviors:
                confidence = float(behavior.get("confidence", 0.0))
                
                # 检查置信度阈值
                if confidence >= config.get("confidence_threshold", 0.7):
                    detected_behaviors.append({
                        "behavior_name": behavior.get("behavior_name", "未知"),
                        "behavior_code": behavior.get("behavior_code", "unknown"),
                        "confidence": confidence,
                        "description": behavior.get("description", ""),
                        "location": behavior.get("location", ""),
                        "severity": self._determine_severity(behavior.get("behavior_code", ""), config),
                        "timestamp": timestamp.isoformat()
                    })
            
            # 构建置信度得分映射
            confidence_scores = {
                behavior["behavior_code"]: behavior["confidence"] 
                for behavior in detected_behaviors
            }
            
            return AnalysisResult(
                camera_id=camera_id,
                timestamp=timestamp,
                detected_behaviors=detected_behaviors,
                confidence_scores=confidence_scores,
                description=parsed_result.get("scene_description", content),
                raw_response=ai_response,
                processing_time=processing_time,
                source_frames=source_frames  # 包含原始帧数据
            )
            
        except Exception as e:
            logger.error(f"解析AI分析结果失败: {str(e)}")
            return self._create_empty_result(camera_id, timestamp, start_time)
    
    def _fallback_parse_response(self, content: str) -> Dict[str, Any]:
        """备用响应解析"""
        # 简单的关键词匹配
        detected_behaviors = []
        
        # 常见异常行为关键词
        behavior_keywords = {
            "fighting": ["打架", "打斗", "冲突", "暴力"],
            "falling": ["摔倒", "跌倒", "摔跤"],
            "damage": ["破坏", "损坏", "砸坏", "踢打"],
            "loitering": ["滞留", "徘徊", "逗留"]
        }
        
        content_lower = content.lower()
        
        for behavior_code, keywords in behavior_keywords.items():
            for keyword in keywords:
                if keyword in content:
                    detected_behaviors.append({
                        "behavior_name": keyword,
                        "behavior_code": behavior_code,
                        "confidence": 0.6,  # 降低置信度
                        "description": f"通过关键词'{keyword}'检测到",
                        "location": ""
                    })
                    break
        
        return {
            "detected_behaviors": detected_behaviors,
            "scene_description": content,
            "risk_level": "low"
        }
    
    def _determine_severity(self, behavior_code: str, config: Dict[str, Any]) -> str:
        """确定行为严重等级"""
        # 从配置中查找对应行为的严重等级
        behaviors = config.get("behaviors", [])
        
        for behavior_config in behaviors:
            behavior = behavior_config["behavior"]
            if behavior.code == behavior_code:
                mapping = behavior_config["mapping"]
                return mapping.severity_override or behavior.default_severity
        
        return "medium"  # 默认中等严重
    
    def _create_empty_result(self, camera_id: int, timestamp: datetime, start_time: float) -> AnalysisResult:
        """创建空的分析结果"""
        return AnalysisResult(
            camera_id=camera_id,
            timestamp=timestamp,
            detected_behaviors=[],
            confidence_scores={},
            description="未检测到异常行为",
            raw_response={},
            processing_time=time.time() - start_time
        )
    
    async def close(self):
        """关闭分析器"""
        await self.qwen_client.close()
        logger.info("AI分析器已关闭") 