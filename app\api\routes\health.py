"""
系统健康检查API路由
"""
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.database import get_db_session
from app.services.storage.file_storage_service import file_storage_service
from app.services.notification.websocket_service import websocket_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/health", tags=["健康检查"])


@router.get("/")
async def health_check():
    """基础健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "智能视频监控预警系统",
        "version": "1.0.0"
    }


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db_session)):
    """详细健康检查"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "智能视频监控预警系统",
        "version": "1.0.0",
        "components": {}
    }
    
    overall_healthy = True
    
    # 检查数据库连接
    try:
        await db.execute("SELECT 1")
        health_status["components"]["database"] = {
            "status": "healthy",
            "message": "数据库连接正常"
        }
    except Exception as e:
        overall_healthy = False
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "message": f"数据库连接失败: {str(e)}"
        }
        logger.error(f"数据库健康检查失败: {str(e)}")
    
    # 检查文件存储服务
    try:
        if file_storage_service.health_check():
            health_status["components"]["file_storage"] = {
                "status": "healthy",
                "message": "文件存储服务正常"
            }
        else:
            overall_healthy = False
            health_status["components"]["file_storage"] = {
                "status": "unhealthy",
                "message": "文件存储服务异常"
            }
    except Exception as e:
        overall_healthy = False
        health_status["components"]["file_storage"] = {
            "status": "unhealthy",
            "message": f"文件存储服务检查失败: {str(e)}"
        }
        logger.error(f"文件存储健康检查失败: {str(e)}")
    
    # 检查WebSocket服务
    try:
        stats = websocket_service.get_connection_stats()
        health_status["components"]["websocket"] = {
            "status": "healthy",
            "message": "WebSocket服务正常",
            "connections": stats["total_connections"]
        }
    except Exception as e:
        overall_healthy = False
        health_status["components"]["websocket"] = {
            "status": "unhealthy",
            "message": f"WebSocket服务检查失败: {str(e)}"
        }
        logger.error(f"WebSocket健康检查失败: {str(e)}")
    
    # 检查AI服务（通义千问API）
    try:
        # 这里可以添加AI服务的健康检查
        # 暂时标记为健康，实际应该调用AI服务进行测试
        health_status["components"]["ai_service"] = {
            "status": "healthy",
            "message": "AI分析服务正常"
        }
    except Exception as e:
        overall_healthy = False
        health_status["components"]["ai_service"] = {
            "status": "unhealthy",
            "message": f"AI服务检查失败: {str(e)}"
        }
        logger.error(f"AI服务健康检查失败: {str(e)}")
    
    # 设置整体状态
    health_status["status"] = "healthy" if overall_healthy else "unhealthy"
    
    # 根据整体健康状态返回相应的HTTP状态码
    if overall_healthy:
        return JSONResponse(status_code=200, content=health_status)
    else:
        return JSONResponse(status_code=503, content=health_status)


@router.get("/metrics")
async def get_metrics():
    """获取系统指标"""
    try:
        import psutil
        import os
        
        # 系统资源指标
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # WebSocket连接统计
        ws_stats = websocket_service.get_connection_stats()
        
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "system": {
                "cpu_usage_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "process_id": os.getpid()
            },
            "websocket": ws_stats,
            "service": {
                "uptime": "计算中...",  # 这里可以添加服务启动时间计算
                "status": "running"
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")


@router.get("/status")
async def get_service_status():
    """获取服务状态"""
    try:
        # 模拟获取各个服务的状态
        status = {
            "timestamp": datetime.now().isoformat(),
            "services": {
                "video_processor": {
                    "status": "running",
                    "active_streams": 0,  # 这里应该从视频处理服务获取实际数据
                    "processed_frames": 0
                },
                "ai_analyzer": {
                    "status": "ready",
                    "queue_size": 0,
                    "processing_time_avg": 0
                },
                "alert_service": {
                    "status": "running",
                    "pending_alerts": 0,
                    "processed_alerts": 0
                },
                "websocket_service": {
                    "status": "running",
                    "connections": websocket_service.get_connection_stats()["total_connections"]
                }
            }
        }
        
        return status
        
    except Exception as e:
        logger.error(f"获取服务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取服务状态失败: {str(e)}")


@router.post("/restart")
async def restart_service():
    """重启服务（仅用于开发环境）"""
    if not settings.is_development:
        raise HTTPException(status_code=403, detail="仅在开发环境中可用")
    
    try:
        # 这里可以添加服务重启逻辑
        # 注意：在生产环境中不应该提供此接口
        
        return {
            "message": "服务重启请求已接收",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"服务重启失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务重启失败: {str(e)}")


@router.get("/ping")
async def ping():
    """简单的ping检查"""
    return {"message": "pong", "timestamp": datetime.now().isoformat()} 