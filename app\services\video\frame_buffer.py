"""
视频帧缓冲区管理器

主要功能：
1. 线程安全的帧缓冲区管理
2. 基于时间窗口的帧聚合
3. 内存使用优化和自动清理
4. 支持批量帧处理
5. 帧优先级管理
"""

import asyncio
import logging
import time
from collections import deque, defaultdict
from dataclasses import dataclass, field
from threading import Lock, RLock
from typing import Dict, List, Optional, Tuple, Any, Callable
import cv2
import numpy as np
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class VideoFrame:
    """视频帧数据结构"""
    frame_id: str
    source_id: str
    timestamp: float
    frame_data: np.ndarray
    frame_index: int
    metadata: Dict[str, Any] = field(default_factory=dict)
    motion_score: Optional[float] = None
    is_key_frame: bool = False
    processing_priority: int = 0  # 0=最高优先级，数字越大优先级越低
    
    def __post_init__(self):
        """帧数据验证"""
        if self.frame_data is None:
            raise ValueError("帧数据不能为空")
        if not isinstance(self.frame_data, np.ndarray):
            raise ValueError("帧数据必须是numpy数组")
    
    @property
    def size_bytes(self) -> int:
        """获取帧数据大小（字节）"""
        if self.frame_data is not None:
            return self.frame_data.nbytes
        return 0
    
    @property
    def age_seconds(self) -> float:
        """获取帧的年龄（秒）"""
        return time.time() - self.timestamp


@dataclass
class FrameBuffer:
    """单个视频源的帧缓冲区"""
    source_id: str
    max_size: int = 100
    max_age_seconds: float = 30.0
    frames: deque = field(default_factory=deque)
    total_size_bytes: int = 0
    last_cleanup_time: float = field(default_factory=time.time)
    _lock: RLock = field(default_factory=RLock)
    
    def add_frame(self, frame: VideoFrame) -> bool:
        """添加帧到缓冲区"""
        with self._lock:
            # 检查缓冲区大小限制
            if len(self.frames) >= self.max_size:
                self._remove_oldest_frame()
            
            # 添加新帧
            self.frames.append(frame)
            self.total_size_bytes += frame.size_bytes
            
            # 定期清理过期帧
            current_time = time.time()
            if current_time - self.last_cleanup_time > 5.0:  # 每5秒清理一次
                self._cleanup_expired_frames()
                self.last_cleanup_time = current_time
            
            return True
    
    def get_frames(self, count: int = None, max_age: float = None) -> List[VideoFrame]:
        """获取帧列表"""
        with self._lock:
            frames = list(self.frames)
            
            # 按时间过滤
            if max_age is not None:
                current_time = time.time()
                frames = [f for f in frames if (current_time - f.timestamp) <= max_age]
            
            # 按数量限制
            if count is not None:
                frames = frames[-count:]  # 获取最新的N帧
            
            return frames
    
    def get_latest_frame(self) -> Optional[VideoFrame]:
        """获取最新帧"""
        with self._lock:
            return self.frames[-1] if self.frames else None
    
    def get_batch_frames(self, batch_size: int = 5, min_motion_score: float = None) -> List[VideoFrame]:
        """获取批量帧用于AI分析"""
        with self._lock:
            frames = list(self.frames)
            
            # 按运动分数过滤
            if min_motion_score is not None:
                frames = [f for f in frames if f.motion_score and f.motion_score >= min_motion_score]
            
            # 按优先级排序
            frames.sort(key=lambda x: (x.processing_priority, -x.timestamp))
            
            return frames[:batch_size]
    
    def clear(self):
        """清空缓冲区"""
        with self._lock:
            self.frames.clear()
            self.total_size_bytes = 0
    
    def _remove_oldest_frame(self):
        """移除最旧的帧"""
        if self.frames:
            oldest_frame = self.frames.popleft()
            self.total_size_bytes -= oldest_frame.size_bytes
    
    def _cleanup_expired_frames(self):
        """清理过期帧"""
        current_time = time.time()
        expired_count = 0
        
        while self.frames:
            oldest_frame = self.frames[0]
            if (current_time - oldest_frame.timestamp) > self.max_age_seconds:
                self.frames.popleft()
                self.total_size_bytes -= oldest_frame.size_bytes
                expired_count += 1
            else:
                break
        
        if expired_count > 0:
            logger.debug(f"清理了 {expired_count} 个过期帧，视频源: {self.source_id}")
    
    @property
    def frame_count(self) -> int:
        """获取帧数量"""
        with self._lock:
            return len(self.frames)
    
    @property
    def size_mb(self) -> float:
        """获取缓冲区大小（MB）"""
        return self.total_size_bytes / (1024 * 1024)


class FrameBufferManager:
    """帧缓冲区管理器
    
    负责管理多个视频源的帧缓冲区，提供统一的帧管理接口
    """
    
    def __init__(self, 
                 max_buffer_size: int = 100,
                 max_frame_age: float = 30.0,
                 max_total_memory_mb: float = 1024.0,
                 cleanup_interval: float = 10.0):
        """
        初始化帧缓冲区管理器
        
        Args:
            max_buffer_size: 每个缓冲区的最大帧数
            max_frame_age: 帧的最大存活时间（秒）
            max_total_memory_mb: 总内存限制（MB）
            cleanup_interval: 清理间隔（秒）
        """
        self.max_buffer_size = max_buffer_size
        self.max_frame_age = max_frame_age
        self.max_total_memory_bytes = max_total_memory_mb * 1024 * 1024
        self.cleanup_interval = cleanup_interval
        
        self._buffers: Dict[str, FrameBuffer] = {}
        self._lock = RLock()
        self._stats = defaultdict(int)
        self._last_memory_check = time.time()
        
        # 启动后台清理任务
        self._cleanup_task = None
        self._running = False
    
    async def start(self):
        """启动管理器"""
        self._running = True
        self._cleanup_task = asyncio.create_task(self._background_cleanup())
        logger.info("帧缓冲区管理器已启动")
    
    async def stop(self):
        """停止管理器"""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        logger.info("帧缓冲区管理器已停止")
    
    def add_frame(self, source_id: str, frame: VideoFrame) -> bool:
        """添加帧到指定源的缓冲区"""
        with self._lock:
            # 确保缓冲区存在
            if source_id not in self._buffers:
                self._buffers[source_id] = FrameBuffer(
                    source_id=source_id,
                    max_size=self.max_buffer_size,
                    max_age_seconds=self.max_frame_age
                )
            
            # 内存检查
            if self._should_check_memory():
                self._enforce_memory_limit()
            
            # 添加帧
            success = self._buffers[source_id].add_frame(frame)
            if success:
                self._stats['frames_added'] += 1
            
            return success
    
    def get_buffer(self, source_id: str) -> Optional[FrameBuffer]:
        """获取指定源的缓冲区"""
        with self._lock:
            return self._buffers.get(source_id)
    
    def get_frames(self, source_id: str, count: int = None, max_age: float = None) -> List[VideoFrame]:
        """获取指定源的帧列表"""
        buffer = self.get_buffer(source_id)
        if buffer:
            return buffer.get_frames(count, max_age)
        return []
    
    def get_latest_frame(self, source_id: str) -> Optional[VideoFrame]:
        """获取指定源的最新帧"""
        buffer = self.get_buffer(source_id)
        if buffer:
            return buffer.get_latest_frame()
        return None
    
    def get_batch_frames(self, source_id: str, batch_size: int = 5, min_motion_score: float = None) -> List[VideoFrame]:
        """获取批量帧用于AI分析"""
        buffer = self.get_buffer(source_id)
        if buffer:
            return buffer.get_batch_frames(batch_size, min_motion_score)
        return []
    
    def get_all_sources(self) -> List[str]:
        """获取所有视频源ID"""
        with self._lock:
            return list(self._buffers.keys())
    
    def remove_source(self, source_id: str):
        """移除指定视频源的缓冲区"""
        with self._lock:
            if source_id in self._buffers:
                self._buffers[source_id].clear()
                del self._buffers[source_id]
                logger.info(f"移除视频源缓冲区: {source_id}")
    
    def clear_all(self):
        """清空所有缓冲区"""
        with self._lock:
            for buffer in self._buffers.values():
                buffer.clear()
            self._buffers.clear()
            logger.info("清空所有帧缓冲区")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            total_frames = sum(buffer.frame_count for buffer in self._buffers.values())
            total_memory_mb = sum(buffer.size_mb for buffer in self._buffers.values())
            
            return {
                'total_sources': len(self._buffers),
                'total_frames': total_frames,
                'total_memory_mb': round(total_memory_mb, 2),
                'memory_limit_mb': self.max_total_memory_bytes / (1024 * 1024),
                'memory_usage_pct': round((total_memory_mb / (self.max_total_memory_bytes / (1024 * 1024))) * 100, 2),
                'frames_added': self._stats['frames_added'],
                'cleanup_runs': self._stats['cleanup_runs'],
                'memory_enforcements': self._stats['memory_enforcements'],
                'sources': {
                    source_id: {
                        'frame_count': buffer.frame_count,
                        'memory_mb': round(buffer.size_mb, 2),
                        'oldest_frame_age': buffer.frames[0].age_seconds if buffer.frames else 0,
                        'newest_frame_age': buffer.frames[-1].age_seconds if buffer.frames else 0,
                    }
                    for source_id, buffer in self._buffers.items()
                }
            }
    
    def _should_check_memory(self) -> bool:
        """是否应该检查内存使用"""
        current_time = time.time()
        if current_time - self._last_memory_check > 5.0:  # 每5秒检查一次
            self._last_memory_check = current_time
            return True
        return False
    
    def _enforce_memory_limit(self):
        """强制执行内存限制"""
        total_memory = sum(buffer.total_size_bytes for buffer in self._buffers.values())
        
        if total_memory > self.max_total_memory_bytes:
            # 按内存使用量排序，优先清理占用内存最多的缓冲区
            sorted_buffers = sorted(
                self._buffers.items(),
                key=lambda x: x[1].total_size_bytes,
                reverse=True
            )
            
            freed_memory = 0
            for source_id, buffer in sorted_buffers:
                if total_memory - freed_memory <= self.max_total_memory_bytes:
                    break
                
                # 清理一半的帧
                frames_to_remove = len(buffer.frames) // 2
                for _ in range(frames_to_remove):
                    if buffer.frames:
                        old_frame = buffer.frames.popleft()
                        buffer.total_size_bytes -= old_frame.size_bytes
                        freed_memory += old_frame.size_bytes
            
            self._stats['memory_enforcements'] += 1
            logger.warning(f"强制清理内存: 释放了 {freed_memory / (1024 * 1024):.2f} MB")
    
    async def _background_cleanup(self):
        """后台清理任务"""
        while self._running:
            try:
                await asyncio.sleep(self.cleanup_interval)
                
                with self._lock:
                    for buffer in self._buffers.values():
                        buffer._cleanup_expired_frames()
                    
                    self._stats['cleanup_runs'] += 1
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"后台清理任务出错: {e}")
                await asyncio.sleep(5) 