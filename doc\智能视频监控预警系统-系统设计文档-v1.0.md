# 智能视频监控预警系统 - 系统设计文档

**版本：** v1.3 (简化高效架构)  
**创建日期：** 2024-12-19  
**更新日期：** 2024-12-19  
**项目代号：** IVMS (Intelligent Video Monitoring System)  
**文档类型：** 系统设计文档（含性能优化架构）

**v1.3 更新说明：**
- 移除YOLO依赖，采用"简化而高效"的架构设计
- 优化预过滤层，使用OpenCV运动检测和帧差异检测
- 专注于大模型一体化分析，避免过度设计
- 调整成本预估，更贴近实际的简化架构效果

## 目录
1. [系统架构设计](#1-系统架构设计)
2. [性能优化架构设计](#2-性能优化架构设计) **[新增]**
3. [核心模块设计](#3-核心模块设计)
4. [数据流设计](#4-数据流设计)
5. [数据库设计](#5-数据库设计)
6. [接口设计](#6-接口设计)
7. [部署架构设计](#7-部署架构设计)
8. [技术选型](#8-技术选型)
9. [代码改进建议](#9-代码改进建议)

## 1. 系统架构设计

### 1.1 整体架构（配置驱动版）

```mermaid
graph TB
    subgraph "视频输入层"
        A1[RTSP视频流1] --> VM[视频管理器]
        A2[RTSP视频流2] --> VM
        A3[RTSP视频流N] --> VM
        A4[本地视频文件] --> VM
    end
    
    subgraph "配置管理层 (新增)"
        CM[配置管理器] --> CC[配置缓存]
        CM --> CW[配置监听器]
        DB_CONFIG[(配置数据库)] --> CM
        CC --> RE[规则引擎]
        CC --> PB[提示词构建器]
    end
    
    subgraph "视频处理层"
        VM --> VP1[视频处理器1]
        VM --> VP2[视频处理器2]
        VM --> VPN[视频处理器N]
        VP1 --> FS[帧调度器]
        VP2 --> FS
        VPN --> FS
    end
    
    subgraph "AI分析层"
        FS --> AD[异常检测器]
        RE --> AD
        AD --> QC[通义千问客户端]
        PB --> QC
        QC --> AD
    end
    
    subgraph "业务逻辑层"
        AD --> AS[预警服务]
        AS --> DM[数据管理器]
        AS --> NS[通知服务]
        DM --> DB[(MySQL数据库)]
        DM --> FS_STORAGE[文件存储服务]
    end
    
    subgraph "接口层"
        NS --> WS[WebSocket服务]
        NS --> MQ[消息队列]
        AS --> API[REST API]
        CM --> CONFIG_API[配置API]
    end
    
    subgraph "外部存储"
        FS_STORAGE --> MINIO[MinIO对象存储]
        DB --> MYSQL[(MySQL 8.0)]
        DB_CONFIG --> MYSQL
    end
    
    subgraph "缓存层"
        REDIS[Redis缓存] --> CC
        REDIS --> AD
        REDIS --> PB
    end
```

### 1.2 系统分层架构

#### 1.2.1 接入层 (Access Layer)
- **视频流接入**：支持RTSP/RTMP/HTTP等协议
- **负载均衡**：分发视频流到不同的处理节点
- **连接管理**：管理视频流连接状态和重连机制

#### 1.2.2 配置管理层 (Configuration Layer) **[新增]**
- **配置管理器**：统一管理所有检测配置
- **规则引擎**：基于配置驱动的规则执行
- **提示词构建器**：动态构建AI提示词
- **配置缓存**：Redis缓存配置提升性能
- **配置监听器**：监听配置变更并热更新

#### 1.2.3 处理层 (Processing Layer)
- **视频解码**：解码不同格式的视频流
- **帧提取**：智能帧采样和缓冲管理
- **预处理**：图像预处理和格式转换

#### 1.2.4 分析层 (Analysis Layer)
- **AI推理**：多模态大模型推理（配置驱动）
- **特征提取**：视频特征和行为分析
- **异常检测**：基于配置规则和AI的异常检测

#### 1.2.5 业务层 (Business Layer)
- **预警管理**：预警信息生成和管理
- **数据存储**：结构化数据和文件存储
- **通知服务**：多渠道通知推送

#### 1.2.6 接口层 (Interface Layer)
- **API网关**：统一的API入口
- **WebSocket服务**：实时消息推送
- **消息队列**：异步消息处理
- **配置API**：配置管理接口

### 1.3 配置驱动核心架构 **[重点]**

```mermaid
graph TD
    subgraph "配置数据源"
        AB[anomaly_behaviors] --> CM[配置管理器]
        AR[analysis_rules] --> CM
        RBM[rule_behavior_mappings] --> CM
        PT[prompt_templates] --> CM
    end
    
    subgraph "配置加载策略"
        CM --> CL[配置加载器]
        CL --> CP[配置解析器]
        CP --> CV[配置验证器]
        CV --> CC[配置缓存]
    end
    
    subgraph "配置使用"
        CC --> |摄像头配置| RE[规则引擎]
        CC --> |提示词模板| PB[提示词构建器]
        CC --> |行为定义| AD[异常检测器]
    end
    
    subgraph "配置热更新"
        CW[配置监听器] --> CM
        CW --> |通知| NS[通知服务]
        NS --> |更新| RE
        NS --> |更新| PB
        NS --> |更新| AD
    end
```

## 2. 性能优化架构设计 **[新增]**

### 2.1 三层性能优化架构

基于用户需求分析，系统需要支持80-100路视频流的高并发处理，并且要有效控制AI API调用成本。我们采用**"简化而高效"**的设计理念，避免过度设计，设计了三层性能优化架构：

**设计理念说明：**
- **去掉YOLO**: 避免系统复杂度，减少维护两套检测系统的开销，消除YOLO漏检导致大模型无法分析的风险
- **专注OpenCV预过滤**: 使用最简单有效的运动检测和帧差异检测，计算成本极低但效果显著
- **大模型一体化分析**: 充分发挥通义千问的人员识别和行为检测能力，无需额外的人员检测步骤

```
🎥 RTSP视频流 → 🔍 预过滤层 → 📦 聚合层 → 🤖 AI分析层 → ⚡ 预警推送
    ↓           ↓(90%过滤)    ↓(批量化)   ↓(智能检测)   ↓
  原始帧      → 有效帧       → 关键帧    → 异常识别    → 实时告警
```

#### 2.1.1 第一层：预过滤层 (Pre-Filter Layer)
**目标：过滤90%的无效帧，只保留有意义的帧**

**运动检测过滤器**
```python
class MotionFilter:
    """运动检测过滤器"""
    
    def __init__(self, sensitivity=0.3, min_area=500):
        self.bg_subtractor = cv2.createBackgroundSubtractorMOG2()
        self.sensitivity = sensitivity
        self.min_area = min_area
        
    def is_motion_detected(self, frame) -> bool:
        """检测帧中是否有运动"""
        fg_mask = self.bg_subtractor.apply(frame)
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            if cv2.contourArea(contour) > self.min_area:
                return True
        return False
```

**帧差异检测过滤器**
```python
class FrameDifferenceFilter:
    """帧差异检测过滤器"""
    
    def __init__(self, threshold=0.1):
        self.previous_frame = None
        self.difference_threshold = threshold
        
    def has_significant_change(self, frame) -> bool:
        """检测帧间是否有显著变化"""
        if self.previous_frame is None:
            self.previous_frame = frame
            return True
            
        # 计算帧间差异
        diff = cv2.absdiff(frame, self.previous_frame)
        diff_score = np.mean(diff) / 255.0
        
        self.previous_frame = frame
        return diff_score > self.difference_threshold
```

#### 2.1.2 第二层：帧聚合层 (Frame Aggregation Layer)
**目标：优化帧选择策略，支持批量处理**

```python
class FrameAggregator:
    """帧聚合器"""
    
    def __init__(self, window_size=5, aggregation_strategy='keyframe'):
        self.window_size = window_size  # 时间窗口大小(秒)
        self.strategy = aggregation_strategy
        self.frame_buffer = []
        
    def add_frame(self, frame_data):
        """添加帧到缓冲区"""
        self.frame_buffer.append(frame_data)
        
        if self._should_process():
            return self._select_frames()
        return None
        
    def _select_frames(self):
        """根据策略选择关键帧"""
        if self.strategy == 'keyframe':
            return self._select_keyframes()
        elif self.strategy == 'uniform':
            return self._uniform_sampling()
        elif self.strategy == 'motion_peak':
            return self._motion_peak_selection()
```

#### 2.1.3 第三层：AI分析层 (AI Analysis Layer)
**目标：智能批量分析，支持多行为检测**

```python
class SmartAIAnalyzer:
    """智能AI分析器"""
    
    def __init__(self):
        self.qwen_client = QwenVLClient()
        self.batch_processor = BatchProcessor()
        self.result_cache = ResultCache()
        
    async def analyze_frames(self, camera_id: int, frames: List[Frame]):
        """批量分析帧"""
        # 1. 获取摄像头配置的异常行为
        behaviors = await self._get_camera_behaviors(camera_id)
        
        # 2. 构建综合检测提示词
        prompt = await self._build_comprehensive_prompt(behaviors)
        
        # 3. 选择分析模式
        if self._supports_multi_frame():
            return await self._multi_frame_analysis(frames, prompt)
        else:
            return await self._single_frame_analysis(frames, prompt)
```

### 2.2 多摄像头多行为配置架构

#### 2.2.1 配置管理策略

```python
class CameraConfigManager:
    """摄像头配置管理器"""
    
    async def get_camera_analysis_config(self, camera_id: int):
        """获取摄像头的分析配置"""
        # 从数据库获取配置，支持多层级配置覆盖
        config = {
            'analysis_rules': await self._get_analysis_rules(camera_id),
            'behaviors': await self._get_behavior_mappings(camera_id),
            'prompts': await self._get_prompt_templates(camera_id),
            'thresholds': await self._get_threshold_configs(camera_id)
        }
        return config
        
    async def _get_behavior_mappings(self, camera_id: int):
        """获取摄像头关联的异常行为"""
        query = """
        SELECT ab.*, rbm.custom_confidence_threshold, rbm.custom_parameters
        FROM analysis_rules ar
        JOIN rule_behavior_mappings rbm ON ar.id = rbm.rule_id
        JOIN anomaly_behaviors ab ON rbm.behavior_id = ab.id
        WHERE ar.camera_id = %s AND ar.is_enabled = 1 AND ab.is_enabled = 1
        """
        return await self.db.fetch_all(query, [camera_id])
```

#### 2.2.2 动态提示词构建

```python
class DynamicPromptBuilder:
    """动态提示词构建器"""
    
    def __init__(self):
        self.template_cache = {}
        
    async def build_prompt(self, camera_id: int, behaviors: List[dict]):
        """为摄像头构建检测提示词"""
        prompts = []
        
        for behavior in behaviors:
            # 获取行为专用模板或使用默认模板
            template = await self._get_behavior_template(behavior['id'])
            
            # 应用自定义参数
            custom_params = behavior.get('custom_parameters', {})
            prompt = self._apply_template_variables(template, custom_params)
            
            prompts.append({
                'behavior_id': behavior['id'],
                'behavior_name': behavior['name'],
                'prompt': prompt,
                'confidence_threshold': behavior.get('custom_confidence_threshold', 
                                                  behavior['default_confidence_threshold'])
            })
            
        return self._combine_prompts(prompts)
```

### 2.3 性能优化策略

#### 2.3.1 API调用优化

```python
class APIOptimizer:
    """API调用优化器"""
    
    def __init__(self):
        self.call_limiter = AsyncLimiter(max_calls=100, time_window=60)  # 限制调用频率
        self.result_cache = TTLCache(maxsize=1000, ttl=300)  # 5分钟缓存
        
    async def optimized_call(self, frames, prompt, camera_id):
        """优化的API调用"""
        # 1. 检查缓存
        cache_key = self._generate_cache_key(frames, prompt)
        if cache_key in self.result_cache:
            return self.result_cache[cache_key]
            
        # 2. 限制并发调用
        async with self.call_limiter:
            result = await self._call_ai_api(frames, prompt)
            
        # 3. 缓存结果
        self.result_cache[cache_key] = result
        return result
```

#### 2.3.2 资源调度优化

```python
class ResourceScheduler:
    """资源调度器"""
    
    def __init__(self, max_concurrent=10):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.priority_queue = PriorityQueue()
        
    async def schedule_analysis(self, camera_id, frames, priority=1):
        """调度分析任务"""
        task = AnalysisTask(camera_id, frames, priority)
        await self.priority_queue.put(task)
        
        async with self.semaphore:
            return await self._process_task(task)
```

### 2.4 成本控制预估

#### 2.4.1 成本优化效果

| 优化策略 | 成本减少 | 说明 |
|---------|---------|------|
| 运动检测过滤 | 80-90% | 过滤静态无变化帧 |
| 帧差异检测过滤 | 50-60% | 过滤相似场景的重复帧 |
| 批量处理 | 50-60% | 减少API调用次数 |
| 结果缓存 | 30-40% | 避免重复分析相似场景 |
| **综合优化** | **85-90%** | **多策略组合效果** |

#### 2.4.2 性能指标

- **视频流处理能力**: 80-100路并发
- **帧处理延迟**: <500ms
- **AI分析响应**: <2s
- **预警推送延迟**: <100ms
- **资源利用率**: CPU<80%, 内存<16GB

## 3. 核心模块设计

### 3.1 配置管理器 (ConfigurationManager) **[新增核心模块]**

#### 3.1.1 类设计
```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from enum import Enum

@dataclass
class AnomalyBehavior:
    """异常行为配置"""
    id: int
    name: str
    code: str
    category: str
    description: str
    ai_keywords: List[str]
    default_severity: str
    default_confidence_threshold: float
    is_enabled: bool

@dataclass
class AnalysisRule:
    """分析规则配置"""
    id: int
    name: str
    camera_id: int
    rule_type: str
    confidence_threshold: float
    consecutive_frames: int
    time_window_seconds: int
    cooldown_seconds: int
    priority: int
    is_enabled: bool

@dataclass
class PromptTemplate:
    """提示词模板配置"""
    id: int
    name: str
    behavior_id: Optional[int]
    template_content: str
    variables: Dict[str, Any]
    model_type: str
    temperature: float
    max_tokens: int
    is_default: bool

class ConfigurationManager:
    """配置管理器 - 核心配置驱动组件"""
    
    def __init__(self, db_manager, cache_manager):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.config_cache: Dict[str, Any] = {}
        self.config_watchers: List[ConfigWatcher] = []
    
    async def load_camera_configuration(self, camera_id: int) -> CameraConfiguration:
        """加载摄像头完整配置"""
        # 1. 从缓存查找
        cache_key = f"config:camera:{camera_id}"
        if cached_config := await self.cache_manager.get(cache_key):
            return cached_config
        
        # 2. 从数据库加载
        config = await self._build_camera_configuration(camera_id)
        
        # 3. 缓存配置
        await self.cache_manager.set(cache_key, config, ttl=3600)
        
        return config
    
    async def _build_camera_configuration(self, camera_id: int) -> CameraConfiguration:
        """构建摄像头配置对象"""
        # 加载分析规则（按优先级：摄像头 > 门店 > 全局）
        rules = await self._load_analysis_rules(camera_id)
        
        # 加载异常行为定义
        behaviors = await self._load_anomaly_behaviors(rules)
        
        # 加载提示词模板
        templates = await self._load_prompt_templates(behaviors)
        
        return CameraConfiguration(
            camera_id=camera_id,
            analysis_rules=rules,
            behaviors=behaviors,
            templates=templates
        )
    
    async def _load_analysis_rules(self, camera_id: int) -> List[AnalysisRule]:
        """加载分析规则 - 分层查找"""
        # 1. 摄像头专属规则
        camera_rules = await self.db_manager.get_rules_by_camera(camera_id)
        if camera_rules:
            return camera_rules
        
        # 2. 门店级别规则
        camera_info = await self.db_manager.get_camera_info(camera_id)
        store_rules = await self.db_manager.get_rules_by_store(camera_info.store_id)
        if store_rules:
            return store_rules
        
        # 3. 全局默认规则
        global_rules = await self.db_manager.get_global_rules()
        return global_rules
    
    async def reload_configuration(self, camera_id: int) -> None:
        """重新加载配置"""
        cache_key = f"config:camera:{camera_id}"
        await self.cache_manager.delete(cache_key)
        
        # 重新构建配置
        config = await self._build_camera_configuration(camera_id)
        await self.cache_manager.set(cache_key, config, ttl=3600)
        
        # 通知相关组件更新
        await self._notify_config_update(camera_id, config)
    
    async def watch_config_changes(self) -> None:
        """监听配置变更"""
        for watcher in self.config_watchers:
            await watcher.start_watching()
```

### 3.2 规则引擎 (RuleEngine) **[新增核心模块]**

#### 3.2.1 类设计
```python
class RuleEngine:
    """规则引擎 - 基于配置驱动的规则执行"""
    
    def __init__(self, config_manager: ConfigurationManager):
        self.config_manager = config_manager
        self.active_rules: Dict[int, List[AnalysisRule]] = {}
        self.rule_states: Dict[str, RuleState] = {}
    
    async def evaluate_frame(self, camera_id: int, frame: Frame, 
                           ai_result: Dict) -> List[DetectionResult]:
        """评估帧是否触发规则"""
        # 1. 获取摄像头配置
        config = await self.config_manager.load_camera_configuration(camera_id)
        
        # 2. 遍历所有启用的规则
        results = []
        for rule in config.analysis_rules:
            if not rule.is_enabled:
                continue
            
            # 3. 评估规则条件
            detection_result = await self._evaluate_rule(rule, frame, ai_result)
            if detection_result:
                results.append(detection_result)
        
        return results
    
    async def _evaluate_rule(self, rule: AnalysisRule, frame: Frame, 
                           ai_result: Dict) -> Optional[DetectionResult]:
        """评估单个规则"""
        rule_key = f"rule_{rule.id}_{frame.camera_id}"
        
        # 1. 检查冷却时间
        if await self._is_in_cooldown(rule_key, rule.cooldown_seconds):
            return None
        
        # 2. 检查置信度阈值
        if ai_result.get('confidence', 0) < rule.confidence_threshold:
            return None
        
        # 3. 检查连续帧数要求
        consecutive_count = await self._update_consecutive_count(rule_key, True)
        if consecutive_count < rule.consecutive_frames:
            return None
        
        # 4. 生成检测结果
        detection_result = DetectionResult(
            rule_id=rule.id,
            camera_id=frame.camera_id,
            confidence=ai_result['confidence'],
            detected_behaviors=ai_result['behaviors'],
            frame_info=frame.to_dict(),
            trigger_time=frame.timestamp
        )
        
        # 5. 设置冷却状态
        await self._set_cooldown(rule_key, rule.cooldown_seconds)
        
        return detection_result
```

### 2.3 提示词构建器 (PromptBuilder) **[新增核心模块]**

#### 2.3.1 类设计
```python
class PromptBuilder:
    """提示词构建器 - 动态构建AI提示词"""
    
    def __init__(self, config_manager: ConfigurationManager):
        self.config_manager = config_manager
        self.template_cache: Dict[str, PromptTemplate] = {}
    
    async def build_prompt(self, camera_id: int, frame: Frame, 
                         context: Dict[str, Any]) -> str:
        """构建AI分析提示词"""
        # 1. 获取摄像头配置
        config = await self.config_manager.load_camera_configuration(camera_id)
        
        # 2. 选择最合适的提示词模板
        template = await self._select_best_template(config, context)
        
        # 3. 构建模板变量
        variables = await self._build_template_variables(config, frame, context)
        
        # 4. 渲染提示词
        prompt = await self._render_template(template, variables)
        
        return prompt
    
    async def _select_best_template(self, config: CameraConfiguration, 
                                  context: Dict[str, Any]) -> PromptTemplate:
        """选择最佳提示词模板 - 分层查找"""
        # 1. 查找行为专用模板
        if behavior_id := context.get('target_behavior_id'):
            for template in config.templates:
                if template.behavior_id == behavior_id and not template.is_default:
                    return template
        
        # 2. 查找行为默认模板
        if behavior_id := context.get('target_behavior_id'):
            for template in config.templates:
                if template.behavior_id == behavior_id and template.is_default:
                    return template
        
        # 3. 使用全局默认模板
        for template in config.templates:
            if template.behavior_id is None and template.is_default:
                return template
        
        # 4. 兜底模板
        return await self._get_fallback_template()
    
    async def _build_template_variables(self, config: CameraConfiguration,
                                      frame: Frame, context: Dict[str, Any]) -> Dict[str, Any]:
        """构建模板变量"""
        # 获取摄像头信息
        camera_info = await self.config_manager.db_manager.get_camera_info(frame.camera_id)
        
        # 获取目标行为定义
        target_behaviors = []
        for rule in config.analysis_rules:
            if rule.is_enabled:
                rule_behaviors = await self._get_rule_behaviors(rule.id)
                target_behaviors.extend(rule_behaviors)
        
        return {
            'camera_location': camera_info.location,
            'target_behaviors': [b.name for b in target_behaviors],
            'behavior_keywords': [kw for b in target_behaviors for kw in b.ai_keywords],
            'confidence_threshold': min([r.confidence_threshold for r in config.analysis_rules]),
            'current_time': frame.timestamp.isoformat(),
            'frame_index': frame.index,
            **context  # 合并额外的上下文变量
        }
    
    async def _render_template(self, template: PromptTemplate, 
                             variables: Dict[str, Any]) -> str:
        """渲染模板"""
        from jinja2 import Template
        
        jinja_template = Template(template.template_content)
        rendered_prompt = jinja_template.render(**variables)
        
        return rendered_prompt
```

### 2.4 视频管理器 (VideoManager)

#### 2.4.1 类设计
```python
class VideoManager:
    """视频源管理器 - 负责管理多路视频流"""
    
    def __init__(self, config_manager: ConfigurationManager):
        self.config_manager = config_manager  # 新增配置管理器依赖
        self.video_sources: Dict[str, VideoSource] = {}
        self.processors: Dict[str, VideoProcessor] = {}
        self.connection_pool = ConnectionPool()
        self.health_checker = HealthChecker()
    
    async def add_video_source(self, source_config: VideoSourceConfig) -> str:
        """添加视频源"""
        # 加载摄像头配置
        camera_config = await self.config_manager.load_camera_configuration(
            source_config.camera_id
        )
        
        # 创建视频处理器（传入配置）
        processor = VideoProcessor(source_config, camera_config)
        
        source_id = f"camera_{source_config.camera_id}"
        self.video_sources[source_id] = VideoSource(source_config)
        self.processors[source_id] = processor
        
        return source_id
    
    async def remove_video_source(self, source_id: str) -> bool:
        """移除视频源"""
        if source_id in self.processors:
            await self.processors[source_id].stop_processing()
            del self.processors[source_id]
            del self.video_sources[source_id]
            return True
        return False
    
    async def get_processor(self, source_id: str) -> VideoProcessor:
        """获取视频处理器"""
        return self.processors.get(source_id)
    
    async def monitor_health(self) -> Dict[str, HealthStatus]:
        """监控视频源健康状态"""
        health_status = {}
        for source_id, processor in self.processors.items():
            health_status[source_id] = await processor.get_health_status()
        return health_status
```

### 2.5 视频处理器 (VideoProcessor)

#### 2.5.1 类设计
```python
class VideoProcessor:
    """视频处理器 - 处理单路视频流"""
    
    def __init__(self, source_config: VideoSourceConfig, camera_config: CameraConfiguration):
        self.source_config = source_config
        self.camera_config = camera_config
        self.frame_buffer = FrameBuffer(maxsize=1000)
        self.decoder = VideoDecoder()
        self.preprocessor = ImagePreprocessor()
        self.is_running = False
    
    async def start_processing(self) -> None:
        """启动视频处理"""
        pass
    
    async def stop_processing(self) -> None:
        """停止视频处理"""
        pass
    
    async def get_frame_batch(self, batch_size: int) -> List[Frame]:
        """获取帧批次用于分析"""
        pass
    
    async def handle_connection_error(self, error: Exception) -> None:
        """处理连接错误"""
        pass
```

#### 2.5.2 核心功能
- **视频解码**：支持多种视频格式解码
- **帧缓冲**：维护滑动窗口的帧缓冲区
- **智能采样**：根据分析需求调整帧率
- **异常处理**：处理视频流中断和错误

### 2.6 帧调度器 (FrameScheduler)

#### 2.6.1 类设计
```python
class FrameScheduler:
    """帧调度器 - 调度帧的分析优先级"""
    
    def __init__(self):
        self.priority_queue = PriorityQueue()
        self.processors: List[VideoProcessor] = []
        self.analysis_config = AnalysisConfig()
    
    async def schedule_analysis(self) -> Optional[AnalysisTask]:
        """调度分析任务"""
        pass
    
    def set_priority(self, source_id: str, priority: int) -> None:
        """设置视频源分析优先级"""
        pass
    
    async def get_next_batch(self) -> Optional[FrameBatch]:
        """获取下一个待分析的帧批次"""
        pass
```

#### 2.6.2 调度策略
- **优先级调度**：高优先级视频源优先分析
- **负载均衡**：分析任务均匀分配
- **资源优化**：根据GPU/CPU负载调整调度策略

### 2.7 异常检测器 (AnomalyDetector)

#### 2.7.1 类设计
```python
class AnomalyDetector:
    """异常检测器 - 核心AI分析模块"""
    
    def __init__(self, config: DetectionConfig):
        self.ai_client = QwenVLClient()
        self.prompt_engine = PromptEngine()
        self.rule_engine = RuleEngine()
        self.persistence_filter = PersistenceFilter()
    
    async def detect_anomaly(self, frame_batch: FrameBatch) -> DetectionResult:
        """检测异常行为"""
        pass
    
    async def analyze_with_ai(self, frames: List[Frame], prompt: str) -> AIAnalysisResult:
        """AI分析"""
        pass
    
    def apply_rules(self, ai_result: AIAnalysisResult) -> List[RuleMatch]:
        """应用检测规则"""
        pass
    
    async def filter_false_positives(self, detection: Detection) -> bool:
        """过滤误报"""
        pass
```

#### 2.7.2 检测流程
```mermaid
graph TD
    A[获取帧批次] --> B[预处理]
    B --> C[AI分析]
    C --> D[规则匹配]
    D --> E[置信度计算]
    E --> F{置信度阈值检查}
    F -->|通过| G[持续性检查]
    F -->|不通过| H[记录日志]
    G --> I{持续性检查}
    I -->|通过| J[生成预警]
    I -->|不通过| K[更新持续性状态]
    J --> L[触发回调]
    H --> M[继续监控]
    K --> M
    L --> M
```

### 2.8 预警服务 (AlertService)

#### 2.8.1 类设计
```python
class AlertService:
    """预警服务 - 处理异常检测结果"""
    
    def __init__(self):
        self.data_manager = DataManager()
        self.notification_service = NotificationService()
        self.evidence_manager = EvidenceManager()
    
    async def process_detection(self, detection: DetectionResult) -> Alert:
        """处理检测结果"""
        pass
    
    async def create_alert(self, detection: DetectionResult) -> Alert:
        """创建预警记录"""
        pass
    
    async def save_evidence(self, alert: Alert, frames: List[Frame]) -> List[Evidence]:
        """保存证据文件"""
        pass
    
    async def send_notification(self, alert: Alert) -> NotificationResult:
        """发送通知"""
        pass
```

#### 2.8.2 预警处理流程
```mermaid
graph TD
    A[接收检测结果] --> B[创建预警记录]
    B --> C[保存到数据库]
    C --> D[保存证据文件]
    D --> E[发送通知]
    E --> F[更新预警状态]
    F --> G[记录处理日志]
```

### 2.9 数据管理器 (DataManager)

#### 2.9.1 类设计
```python
class DataManager:
    """数据管理器 - 数据持久化和查询"""
    
    def __init__(self):
        self.db_pool = DatabasePool()
        self.cache = RedisCache()
        self.storage_service = StorageService()
    
    async def save_alert(self, alert: Alert) -> int:
        """保存预警记录"""
        pass
    
    async def get_alerts(self, query: AlertQuery) -> List[Alert]:
        """查询预警记录"""
        pass
    
    async def save_evidence(self, evidence: Evidence) -> str:
        """保存证据文件"""
        pass
    
    async def update_alert_status(self, alert_id: int, status: AlertStatus) -> bool:
        """更新预警状态"""
        pass
```

## 3. 数据流设计

### 3.1 主要数据流

```mermaid
graph TD
    A[视频流] --> B[视频处理器]
    B --> C[帧缓冲区]
    C --> D[帧调度器]
    D --> E[异常检测器]
    E --> F[AI分析]
    F --> G[检测结果]
    G --> H[预警服务]
    H --> I[数据存储]
    H --> J[通知服务]
    I --> K[MySQL数据库]
    I --> L[MinIO存储]
    J --> M[WebSocket推送]
    J --> N[消息队列]
```

### 3.2 数据结构定义

#### 3.2.1 视频帧数据结构
```python
@dataclass
class Frame:
    """视频帧数据结构"""
    timestamp: datetime
    source_id: str
    frame_index: int
    image_data: np.ndarray
    metadata: Dict[str, Any]
    
@dataclass
class FrameBatch:
    """帧批次数据结构"""
    source_id: str
    frames: List[Frame]
    start_time: datetime
    end_time: datetime
    fps: float
```

#### 3.2.2 检测结果数据结构
```python
@dataclass
class DetectionResult:
    """检测结果数据结构"""
    source_id: str
    timestamp: datetime
    anomaly_type: str
    confidence: float
    description: str
    bounding_boxes: List[BoundingBox]
    ai_analysis: str
    
@dataclass
class Alert:
    """预警数据结构"""
    id: int
    uuid: str
    source_id: str
    camera_id: int
    behavior_id: int
    title: str
    description: str
    severity: AlertSeverity
    confidence: float
    status: AlertStatus
    created_at: datetime
    evidence_files: List[str]
```

### 3.3 数据流优化策略

#### 3.3.1 批处理优化
- **帧批次处理**：批量处理视频帧提高效率
- **异步处理**：使用异步IO减少阻塞
- **缓存策略**：缓存频繁访问的数据

#### 3.3.2 内存管理
- **对象池**：复用对象减少GC压力
- **流式处理**：避免大量数据在内存中积累
- **内存监控**：监控内存使用情况，防止内存泄漏

## 4. 数据库设计

### 4.1 核心表结构

基于`database_design.sql`的核心表：

#### 4.1.1 门店表 (stores)
```sql
-- 门店基础信息
CREATE TABLE `stores` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '门店名称',
  `code` varchar(50) NOT NULL COMMENT '门店编号',
  `status` enum('normal','warning','offline','disabled') NOT NULL DEFAULT 'normal',
  `camera_count` int unsigned NOT NULL DEFAULT '0',
  `online_camera_count` int unsigned NOT NULL DEFAULT '0',
  -- 其他字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
);
```

#### 4.1.2 摄像头表 (cameras)
```sql
-- 摄像头设备信息
CREATE TABLE `cameras` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `store_id` bigint unsigned NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '摄像头名称',
  `rtsp_url` varchar(500) NOT NULL COMMENT 'RTSP流地址',
  `status` enum('online','offline','error','maintenance') NOT NULL DEFAULT 'offline',
  `analysis_enabled` tinyint(1) NOT NULL DEFAULT '1',
  -- 其他字段...
  PRIMARY KEY (`id`),
  KEY `idx_store_id` (`store_id`)
);
```

#### 4.1.3 预警表 (alerts)
```sql
-- 预警事件记录
CREATE TABLE `alerts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(36) NOT NULL,
  `store_id` bigint unsigned NOT NULL,
  `camera_id` bigint unsigned NOT NULL,
  `behavior_id` bigint unsigned NOT NULL,
  `title` varchar(200) NOT NULL,
  `description` text DEFAULT NULL,
  `severity` enum('critical','high','medium','low') NOT NULL,
  `confidence_score` decimal(5,4) NOT NULL,
  `status` enum('pending','processing','resolved','false_positive','ignored') NOT NULL DEFAULT 'pending',
  `trigger_time` timestamp NOT NULL,
  `ai_analysis_result` json DEFAULT NULL,
  -- 其他字段...
  PRIMARY KEY (`id`),
  KEY `idx_store_camera` (`store_id`, `camera_id`),
  KEY `idx_trigger_time` (`trigger_time`)
);
```

#### 4.1.4 证据文件表 (evidence_files)
```sql
-- 证据文件记录
CREATE TABLE `evidence_files` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `alert_id` bigint unsigned NOT NULL,
  `file_storage_id` bigint unsigned NOT NULL,
  `evidence_type` enum('auto_capture','manual_upload','system_generated') NOT NULL DEFAULT 'auto_capture',
  `capture_time` timestamp NOT NULL,
  `is_key_evidence` tinyint(1) NOT NULL DEFAULT '0',
  -- 其他字段...
  PRIMARY KEY (`id`),
  KEY `idx_alert_id` (`alert_id`)
);
```

### 4.2 数据库连接设计

#### 4.2.1 连接池配置
```python
class DatabaseConfig:
    """数据库配置"""
    HOST = "localhost"
    PORT = 3306
    DATABASE = "ivms"
    USERNAME = "ivms_user"
    PASSWORD = "secure_password"
    
    # 连接池配置
    POOL_SIZE = 20
    MAX_OVERFLOW = 30
    POOL_TIMEOUT = 30
    POOL_RECYCLE = 3600
```

#### 4.2.2 数据访问层
```python
class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: DatabaseConfig):
        self.engine = create_async_engine(
            f"mysql+aiomysql://{config.USERNAME}:{config.PASSWORD}@{config.HOST}:{config.PORT}/{config.DATABASE}",
            pool_size=config.POOL_SIZE,
            max_overflow=config.MAX_OVERFLOW,
            pool_timeout=config.POOL_TIMEOUT,
            pool_recycle=config.POOL_RECYCLE
        )
        self.session_factory = sessionmaker(self.engine, class_=AsyncSession)
    
    async def execute_query(self, query: str, params: Dict = None) -> List[Dict]:
        """执行查询"""
        pass
    
    async def execute_transaction(self, operations: List[Operation]) -> bool:
        """执行事务"""
        pass
```

## 5. 接口设计

### 5.1 WebSocket接口

#### 5.1.1 预警推送接口
```python
# WebSocket URL: ws://host:port/alerts
# 消息格式
{
    "type": "alert",
    "data": {
        "id": "alert_id",
        "uuid": "alert_uuid",
        "store_id": 1,
        "camera_id": 1,
        "title": "检测到异常行为",
        "description": "在包房A01检测到疑似打斗行为",
        "severity": "high",
        "confidence": 0.85,
        "timestamp": "2024-12-19T10:30:00Z",
        "evidence_files": [
            "video_001.mp4",
            "screenshot_001.jpg"
        ]
    }
}
```

#### 5.1.2 系统状态推送
```python
# 系统状态消息
{
    "type": "system_status",
    "data": {
        "timestamp": "2024-12-19T10:30:00Z",
        "total_cameras": 80,
        "online_cameras": 78,
        "offline_cameras": 2,
        "processing_rate": 1200,  # 每分钟处理帧数
        "alert_count_today": 15
    }
}
```

### 5.2 REST API接口

#### 5.2.1 健康检查接口
```python
# GET /health
{
    "status": "healthy",
    "timestamp": "2024-12-19T10:30:00Z",
    "services": {
        "database": "healthy",
        "storage": "healthy",
        "ai_service": "healthy"
    },
    "metrics": {
        "cpu_usage": 65.5,
        "memory_usage": 12.3,
        "gpu_usage": 75.2
    }
}
```

#### 5.2.2 预警查询接口
```python
# GET /api/alerts?page=1&limit=20&severity=high
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 150,
        "page": 1,
        "limit": 20,
        "items": [
            {
                "id": 1,
                "uuid": "alert_uuid",
                "store_name": "KTV旗舰店",
                "camera_name": "包房A01",
                "title": "检测到异常行为",
                "severity": "high",
                "status": "pending",
                "created_at": "2024-12-19T10:30:00Z"
            }
        ]
    }
}
```

#### 5.2.3 配置管理接口
```python
# GET /api/config
{
    "code": 200,
    "data": {
        "analysis_interval": 10,
        "confidence_threshold": 0.7,
        "buffer_duration": 11,
        "max_concurrent_streams": 80
    }
}

# PUT /api/config
{
    "analysis_interval": 15,
    "confidence_threshold": 0.75
}
```

### 5.3 消息队列接口

#### 5.3.1 Redis消息队列
```python
# 预警消息队列
QUEUE_NAME = "alerts"
MESSAGE_FORMAT = {
    "id": "alert_id",
    "type": "alert",
    "priority": "high",
    "data": {
        # 预警数据
    },
    "timestamp": "2024-12-19T10:30:00Z"
}
```

## 6. 部署架构设计

### 6.1 容器化部署

#### 6.1.1 Docker Compose配置
```yaml
version: '3.8'
services:
  ivms-app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://user:pass@mysql:3306/ivms
      - REDIS_URL=redis://redis:6379
      - MINIO_URL=http://minio:9000
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - mysql
      - redis
      - minio
    
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpass
      - MYSQL_DATABASE=ivms
      - MYSQL_USER=ivms_user
      - MYSQL_PASSWORD=userpass
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database_design.sql:/docker-entrypoint-initdb.d/init.sql
    
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    
  minio:
    image: minio/minio
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"

volumes:
  mysql_data:
  redis_data:
  minio_data:
```

#### 6.1.2 Dockerfile
```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgstreamer1.0-0 \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "main.py"]
```

### 6.2 生产环境部署

#### 6.2.1 硬件架构
```mermaid
graph TB
    subgraph "负载均衡层"
        LB[负载均衡器]
    end
    
    subgraph "应用服务层"
        APP1[应用服务器1]
        APP2[应用服务器2]
        APP3[应用服务器N]
    end
    
    subgraph "AI推理层"
        GPU1[GPU服务器1<br/>RTX 4090]
        GPU2[GPU服务器2<br/>RTX 4090]
    end
    
    subgraph "数据存储层"
        DB[(MySQL主从)]
        REDIS[(Redis集群)]
        MINIO[(MinIO集群)]
    end
    
    LB --> APP1
    LB --> APP2
    LB --> APP3
    
    APP1 --> GPU1
    APP2 --> GPU1
    APP3 --> GPU2
    
    APP1 --> DB
    APP2 --> DB
    APP3 --> DB
    
    APP1 --> REDIS
    APP2 --> REDIS
    APP3 --> REDIS
    
    APP1 --> MINIO
    APP2 --> MINIO
    APP3 --> MINIO
```

#### 6.2.2 服务器配置建议

**GPU推理服务器：**
- CPU: Intel Xeon E5-2680 v4 (14核28线程) 或同等级别
- 内存: 64GB DDR4
- GPU: RTX 4090 24GB
- 存储: 2TB NVMe SSD
- 网络: 万兆网卡

**应用服务器：**
- CPU: Intel Xeon E5-2640 v4 (10核20线程) 或同等级别
- 内存: 32GB DDR4
- 存储: 1TB NVMe SSD
- 网络: 万兆网卡

**数据库服务器：**
- CPU: Intel Xeon E5-2660 v4 (14核28线程) 或同等级别
- 内存: 128GB DDR4
- 存储: 4TB NVMe SSD (RAID 10)
- 网络: 万兆网卡

### 6.3 监控和运维

#### 6.3.1 监控指标
```python
# 系统监控指标
METRICS = {
    "system": {
        "cpu_usage": "CPU使用率",
        "memory_usage": "内存使用率",
        "disk_usage": "磁盘使用率",
        "network_io": "网络IO"
    },
    "business": {
        "video_streams_count": "视频流数量",
        "processing_rate": "处理速度",
        "alert_count": "预警数量",
        "error_rate": "错误率",
        "response_time": "响应时间"
    },
    "ai": {
        "gpu_usage": "GPU使用率",
        "inference_time": "推理时间",
        "model_accuracy": "模型准确率"
    }
}
```

#### 6.3.2 日志管理
```python
# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        },
        "json": {
            "format": "%(asctime)s %(levelname)s %(name)s %(message)s",
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "standard"
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/ivms.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "formatter": "json"
        }
    },
    "loggers": {
        "": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False
        }
    }
}
```

## 7. 技术选型

### 7.1 核心技术栈

| 技术领域 | 选型 | 版本 | 说明 |
|---------|------|------|------|
| 编程语言 | Python | 3.9+ | 丰富的AI和视频处理库 |
| Web框架 | FastAPI | 0.104+ | 高性能异步框架 |
| 数据库 | MySQL | 8.0+ | 成熟稳定的关系型数据库 |
| 缓存 | Redis | 7.0+ | 高性能内存数据库 |
| 消息队列 | Redis/RabbitMQ | - | 异步消息处理 |
| 对象存储 | MinIO | - | S3兼容的对象存储 |
| 视频处理 | OpenCV | 4.8+ | 视频处理和计算机视觉 |
| AI推理 | 通义千问API | - | 多模态大模型服务 |
| 容器化 | Docker | 20.10+ | 容器化部署 |
| 编排 | Docker Compose | 2.0+ | 服务编排 |

### 7.2 依赖库选择

#### 7.2.1 Python核心依赖
```txt
# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0

# 数据库
aiomysql==0.2.0
sqlalchemy[asyncio]==2.0.23
alembic==1.13.0

# 缓存和消息队列
redis==5.0.1
aioredis==2.0.1

# 视频处理
opencv-python==********
numpy==1.24.3
pillow==10.1.0

# AI和机器学习
httpx==0.25.2
openai==1.3.7

# 对象存储
minio==7.2.0
boto3==1.34.0

# 工具库
pydantic==2.5.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
```

### 7.3 架构模式

#### 7.3.1 设计模式
- **单例模式**：数据库连接池、配置管理器
- **工厂模式**：视频处理器创建、AI客户端创建
- **观察者模式**：事件通知、状态变更
- **策略模式**：检测规则、通知策略
- **适配器模式**：不同视频源适配

#### 7.3.2 架构原则
- **单一职责原则**：每个模块专注单一功能
- **开闭原则**：对扩展开放，对修改关闭
- **依赖倒置**：依赖抽象而非具体实现
- **接口隔离**：接口设计简洁专一
- **最小知识原则**：降低模块间的耦合度

## 8. 代码改进建议

### 8.1 基于参考代码的分析

#### 8.1.1 现有代码优点
1. **基础架构合理**：使用FastAPI和WebSocket的架构选择正确
2. **异步处理**：采用了异步编程模型
3. **配置管理**：有基础的配置管理机制
4. **视频处理**：基本的视频流处理逻辑

#### 8.1.2 需要改进的问题

**1. 生产环境适配性不足**
```python
# 问题：缺乏完整的错误处理
# 原代码示例
ret, frame = self.cap.read()
if not ret:
    break  # 简单退出，没有重连机制

# 改进建议
try:
    ret, frame = self.cap.read()
    if not ret:
        await self._handle_stream_error()
        continue
except Exception as e:
    logger.error(f"视频流读取错误: {e}")
    await self._reconnect_stream()
```

**2. 资源管理不完善**
```python
# 问题：缺乏资源池管理
# 原代码示例
cap = cv2.VideoCapture(video_source)

# 改进建议
class VideoConnectionPool:
    def __init__(self, max_connections=100):
        self.pool = asyncio.Queue(maxsize=max_connections)
        self.active_connections = {}
    
    async def get_connection(self, source_id: str):
        if source_id in self.active_connections:
            return self.active_connections[source_id]
        
        connection = await self.pool.get()
        self.active_connections[source_id] = connection
        return connection
```

**3. 数据库集成缺失**
```python
# 问题：只有文件存储，没有数据库
# 改进建议：添加完整的数据库操作
class AlertRepository:
    async def save_alert(self, alert: Alert) -> int:
        query = """
        INSERT INTO alerts (uuid, store_id, camera_id, title, description, 
                          severity, confidence_score, status, trigger_time)
        VALUES (%(uuid)s, %(store_id)s, %(camera_id)s, %(title)s, 
                %(description)s, %(severity)s, %(confidence)s, %(status)s, %(trigger_time)s)
        """
        async with self.db.acquire() as conn:
            result = await conn.execute(query, alert.to_dict())
            return result.lastrowid
```

**4. 监控和日志不完善**
```python
# 问题：简单的print输出
print("start")

# 改进建议：结构化日志
import structlog
logger = structlog.get_logger(__name__)

async def trigger_analysis(self):
    logger.info("开始异常检测分析", 
                source_id=self.source_id,
                buffer_size=len(self.buffer),
                analysis_interval=self.analysis_interval)
```

### 8.2 改进后的核心模块示例

#### 8.2.1 改进的视频处理器
```python
class EnhancedVideoProcessor:
    """改进的视频处理器"""
    
    def __init__(self, config: VideoProcessorConfig):
        self.config = config
        self.connection_pool = VideoConnectionPool()
        self.frame_buffer = CircularBuffer(maxsize=config.buffer_size)
        self.health_checker = HealthChecker()
        self.metrics = MetricsCollector()
        
    async def start_processing(self):
        """启动视频处理，包含完整的错误处理"""
        try:
            await self._initialize_connection()
            await self._start_frame_processing()
        except Exception as e:
            logger.error("视频处理启动失败", error=str(e))
            await self._handle_startup_error(e)
    
    async def _handle_stream_error(self):
        """处理视频流错误"""
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                await asyncio.sleep(2 ** retry_count)  # 指数退避
                await self._reconnect_stream()
                logger.info("视频流重连成功", retry_count=retry_count)
                return
            except Exception as e:
                retry_count += 1
                logger.warning("视频流重连失败", 
                             retry_count=retry_count, 
                             error=str(e))
        
        logger.error("视频流重连达到最大次数，标记为离线")
        await self._mark_stream_offline()
```

#### 8.2.2 改进的异常检测器
```python
class EnhancedAnomalyDetector:
    """改进的异常检测器"""
    
    def __init__(self, config: DetectionConfig):
        self.config = config
        self.ai_client = QwenVLClient(config.ai_config)
        self.rule_engine = RuleEngine()
        self.persistence_filter = PersistenceFilter()
        self.metrics = DetectionMetrics()
        
    async def detect_anomaly(self, frame_batch: FrameBatch) -> DetectionResult:
        """检测异常，包含完整的错误处理和指标收集"""
        start_time = time.time()
        
        try:
            # 预处理
            processed_frames = await self._preprocess_frames(frame_batch.frames)
            
            # AI分析
            ai_result = await self._analyze_with_ai(processed_frames)
            
            # 规则匹配
            rule_results = self._apply_detection_rules(ai_result)
            
            # 置信度计算
            confidence = self._calculate_confidence(ai_result, rule_results)
            
            # 持续性检查
            is_persistent = await self._check_persistence(frame_batch.source_id, ai_result)
            
            detection_result = DetectionResult(
                source_id=frame_batch.source_id,
                timestamp=frame_batch.end_time,
                anomaly_type=ai_result.anomaly_type,
                confidence=confidence,
                description=ai_result.description,
                is_persistent=is_persistent
            )
            
            # 收集指标
            processing_time = time.time() - start_time
            await self.metrics.record_detection(detection_result, processing_time)
            
            return detection_result
            
        except Exception as e:
            logger.error("异常检测失败", 
                        source_id=frame_batch.source_id,
                        error=str(e))
            await self.metrics.record_error(e)
            raise
```

### 8.3 性能优化建议

#### 8.3.1 并发处理优化
```python
# 使用信号量控制并发
class ConcurrencyController:
    def __init__(self, max_concurrent_ai_requests=5):
        self.ai_semaphore = asyncio.Semaphore(max_concurrent_ai_requests)
        self.processing_semaphore = asyncio.Semaphore(80)  # 80路视频流
    
    async def process_with_ai(self, frames):
        async with self.ai_semaphore:
            return await self.ai_client.analyze(frames)
```

#### 8.3.2 内存优化
```python
# 对象池管理
class ObjectPool:
    def __init__(self, factory, max_size=100):
        self.factory = factory
        self.pool = asyncio.Queue(maxsize=max_size)
        self._fill_pool()
    
    async def get_object(self):
        try:
            return self.pool.get_nowait()
        except asyncio.QueueEmpty:
            return self.factory()
    
    async def return_object(self, obj):
        try:
            obj.reset()  # 重置对象状态
            self.pool.put_nowait(obj)
        except asyncio.QueueFull:
            pass  # 池满则丢弃
```

### 8.4 可维护性改进

#### 8.4.1 配置管理改进
```python
from pydantic import BaseSettings

class SystemConfig(BaseSettings):
    """系统配置"""
    
    # 数据库配置
    database_url: str
    database_pool_size: int = 20
    
    # AI配置
    qwen_api_key: str
    qwen_api_url: str
    qwen_model: str = "qwen-vl-max-2025-01-25"
    
    # 性能配置
    max_concurrent_streams: int = 80
    analysis_interval: int = 10
    confidence_threshold: float = 0.7
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
```

#### 8.4.2 测试改进
```python
import pytest
from unittest.mock import AsyncMock, MagicMock

class TestAnomalyDetector:
    @pytest.fixture
    async def detector(self):
        config = DetectionConfig(confidence_threshold=0.7)
        return EnhancedAnomalyDetector(config)
    
    @pytest.mark.asyncio
    async def test_detect_anomaly_success(self, detector):
        # 准备测试数据
        frame_batch = FrameBatch(
            source_id="test_camera_1",
            frames=[create_test_frame()],
            start_time=datetime.now(),
            end_time=datetime.now()
        )
        
        # 模拟AI分析结果
        detector.ai_client.analyze = AsyncMock(return_value=AIResult(
            anomaly_type="violence",
            confidence=0.85,
            description="检测到打斗行为"
        ))
        
        # 执行检测
        result = await detector.detect_anomaly(frame_batch)
        
        # 验证结果
        assert result.anomaly_type == "violence"
        assert result.confidence == 0.85
        assert "打斗" in result.description
```

---

**文档修订历史**

| 版本 | 日期 | 修订内容 | 修订人 |
|------|------|----------|--------|
| v1.0 | 2024-12-19 | 初始版本创建 | 系统架构师 | 