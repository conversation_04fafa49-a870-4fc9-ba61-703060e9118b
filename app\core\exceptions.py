"""
自定义异常类
"""
from datetime import datetime
from typing import Any, Dict, Optional, Union
from enum import Enum


class ErrorCode(Enum):
    """错误码枚举"""
    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = "1000"
    VALIDATION_ERROR = "1001"
    AUTHENTICATION_ERROR = "1002"
    AUTHORIZATION_ERROR = "1003"
    RESOURCE_NOT_FOUND = "1004"
    RESOURCE_ALREADY_EXISTS = "1005"
    INVALID_PARAMETER = "1006"
    
    # 数据库错误 (2000-2999)
    DATABASE_CONNECTION_ERROR = "2000"
    DATABASE_QUERY_ERROR = "2001"
    DATABASE_TRANSACTION_ERROR = "2002"
    DATABASE_CONSTRAINT_ERROR = "2003"
    
    # 视频流错误 (3000-3999)
    VIDEO_STREAM_CONNECTION_ERROR = "3000"
    VIDEO_STREAM_TIMEOUT_ERROR = "3001"
    VIDEO_STREAM_FORMAT_ERROR = "3002"
    VIDEO_FRAME_PROCESSING_ERROR = "3003"
    VIDEO_STREAM_NOT_FOUND = "3004"
    VIDEO_STREAM_ALREADY_EXISTS = "3005"
    
    # AI分析错误 (4000-4999)
    AI_API_ERROR = "4000"
    AI_API_TIMEOUT = "4001"
    AI_API_QUOTA_EXCEEDED = "4002"
    AI_API_INVALID_RESPONSE = "4003"
    AI_ANALYSIS_ERROR = "4004"
    AI_MODEL_NOT_AVAILABLE = "4005"
    
    # 预警系统错误 (5000-5999)
    ALERT_CREATION_ERROR = "5000"
    ALERT_NOT_FOUND = "5001"
    ALERT_ALREADY_PROCESSED = "5002"
    EVIDENCE_UPLOAD_ERROR = "5003"
    NOTIFICATION_SEND_ERROR = "5004"
    
    # 存储系统错误 (6000-6999)
    STORAGE_CONNECTION_ERROR = "6000"
    STORAGE_UPLOAD_ERROR = "6001"
    STORAGE_DOWNLOAD_ERROR = "6002"
    STORAGE_DELETE_ERROR = "6003"
    STORAGE_FILE_NOT_FOUND = "6004"
    
    # 配置错误 (7000-7999)
    CONFIG_NOT_FOUND = "7000"
    CONFIG_INVALID_FORMAT = "7001"
    CONFIG_VALIDATION_ERROR = "7002"
    
    # 系统错误 (8000-8999)
    SYSTEM_RESOURCE_EXHAUSTED = "8000"
    SYSTEM_SERVICE_UNAVAILABLE = "8001"
    SYSTEM_INTERNAL_ERROR = "8002"
    
    # 业务逻辑错误 (9000-9999)
    BUSINESS_RULE_VIOLATION = "9000"
    INSUFFICIENT_PERMISSIONS = "9001"
    OPERATION_NOT_ALLOWED = "9002"


class VideoAIException(Exception):
    """视频AI系统基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: Union[ErrorCode, str] = ErrorCode.UNKNOWN_ERROR,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        self.message = message
        self.error_code = error_code.value if isinstance(error_code, ErrorCode) else error_code
        self.status_code = status_code
        self.details = details or {}
        self.original_exception = original_exception
        self.timestamp = datetime.now()
        
        super().__init__(self.message)
    
    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"
    
    def __repr__(self) -> str:
        return (
            f"{self.__class__.__name__}("
            f"message={self.message!r}, "
            f"error_code={self.error_code!r}, "
            f"status_code={self.status_code})"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error": True,
            "message": self.message,
            "error_code": self.error_code,
            "status_code": self.status_code,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
        }


class ValidationException(VideoAIException):
    """验证异常"""
    
    def __init__(
        self,
        message: str = "参数验证失败",
        field: Optional[str] = None,
        value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if field:
            error_details["field"] = field
        if value is not None:
            error_details["value"] = value
        
        super().__init__(
            message=message,
            error_code=ErrorCode.VALIDATION_ERROR,
            status_code=422,
            details=error_details
        )


class AuthenticationException(VideoAIException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTHENTICATION_ERROR,
            status_code=401
        )


class AuthorizationException(VideoAIException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足"):
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTHORIZATION_ERROR,
            status_code=403
        )


class ResourceNotFoundException(VideoAIException):
    """资源未找到异常"""
    
    def __init__(
        self,
        message: str = "资源未找到",
        resource_type: Optional[str] = None,
        resource_id: Optional[Union[str, int]] = None
    ):
        details = {}
        if resource_type:
            details["resource_type"] = resource_type
        if resource_id is not None:
            details["resource_id"] = str(resource_id)
        
        super().__init__(
            message=message,
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            status_code=404,
            details=details
        )


class ResourceAlreadyExistsException(VideoAIException):
    """资源已存在异常"""
    
    def __init__(
        self,
        message: str = "资源已存在",
        resource_type: Optional[str] = None,
        resource_id: Optional[Union[str, int]] = None
    ):
        details = {}
        if resource_type:
            details["resource_type"] = resource_type
        if resource_id is not None:
            details["resource_id"] = str(resource_id)
        
        super().__init__(
            message=message,
            error_code=ErrorCode.RESOURCE_ALREADY_EXISTS,
            status_code=409,
            details=details
        )


class DatabaseException(VideoAIException):
    """数据库异常"""
    
    def __init__(
        self,
        message: str = "数据库操作失败",
        operation: Optional[str] = None,
        table: Optional[str] = None,
        original_exception: Optional[Exception] = None
    ):
        details = {}
        if operation:
            details["operation"] = operation
        if table:
            details["table"] = table
        
        super().__init__(
            message=message,
            error_code=ErrorCode.DATABASE_QUERY_ERROR,
            status_code=500,
            details=details,
            original_exception=original_exception
        )


class VideoStreamException(VideoAIException):
    """视频流异常"""
    
    def __init__(
        self,
        message: str = "视频流处理失败",
        stream_url: Optional[str] = None,
        camera_id: Optional[Union[str, int]] = None,
        error_code: ErrorCode = ErrorCode.VIDEO_STREAM_CONNECTION_ERROR,
        original_exception: Optional[Exception] = None
    ):
        details = {}
        if stream_url:
            details["stream_url"] = stream_url
        if camera_id is not None:
            details["camera_id"] = str(camera_id)
        
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=500,
            details=details,
            original_exception=original_exception
        )


class AIAnalysisException(VideoAIException):
    """AI分析异常"""
    
    def __init__(
        self,
        message: str = "AI分析失败",
        model: Optional[str] = None,
        api_response: Optional[Dict[str, Any]] = None,
        error_code: ErrorCode = ErrorCode.AI_ANALYSIS_ERROR,
        original_exception: Optional[Exception] = None
    ):
        details = {}
        if model:
            details["model"] = model
        if api_response:
            details["api_response"] = api_response
        
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=500,
            details=details,
            original_exception=original_exception
        )


class AlertException(VideoAIException):
    """预警系统异常"""
    
    def __init__(
        self,
        message: str = "预警系统操作失败",
        alert_id: Optional[Union[str, int]] = None,
        camera_id: Optional[Union[str, int]] = None,
        error_code: ErrorCode = ErrorCode.ALERT_CREATION_ERROR,
        original_exception: Optional[Exception] = None
    ):
        details = {}
        if alert_id is not None:
            details["alert_id"] = str(alert_id)
        if camera_id is not None:
            details["camera_id"] = str(camera_id)
        
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=500,
            details=details,
            original_exception=original_exception
        )


class StorageException(VideoAIException):
    """存储系统异常"""
    
    def __init__(
        self,
        message: str = "存储操作失败",
        file_path: Optional[str] = None,
        bucket: Optional[str] = None,
        error_code: ErrorCode = ErrorCode.STORAGE_CONNECTION_ERROR,
        original_exception: Optional[Exception] = None
    ):
        details = {}
        if file_path:
            details["file_path"] = file_path
        if bucket:
            details["bucket"] = bucket
        
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=500,
            details=details,
            original_exception=original_exception
        )


class ConfigurationException(VideoAIException):
    """配置异常"""
    
    def __init__(
        self,
        message: str = "配置错误",
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        error_code: ErrorCode = ErrorCode.CONFIG_NOT_FOUND
    ):
        details = {}
        if config_key:
            details["config_key"] = config_key
        if config_value is not None:
            details["config_value"] = str(config_value)
        
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=500,
            details=details
        )


class BusinessLogicException(VideoAIException):
    """业务逻辑异常"""
    
    def __init__(
        self,
        message: str = "业务规则违反",
        rule: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        details = context or {}
        if rule:
            details["rule"] = rule
        
        super().__init__(
            message=message,
            error_code=ErrorCode.BUSINESS_RULE_VIOLATION,
            status_code=400,
            details=details
        )


# 异常工厂函数
def create_database_exception(
    operation: str,
    table: str = None,
    original_exception: Exception = None
) -> DatabaseException:
    """创建数据库异常"""
    return DatabaseException(
        message=f"数据库{operation}操作失败",
        operation=operation,
        table=table,
        original_exception=original_exception
    )


def create_video_stream_exception(
    camera_id: Union[str, int],
    stream_url: str = None,
    error_type: str = "connection",
    original_exception: Exception = None
) -> VideoStreamException:
    """创建视频流异常"""
    error_code_map = {
        "connection": ErrorCode.VIDEO_STREAM_CONNECTION_ERROR,
        "timeout": ErrorCode.VIDEO_STREAM_TIMEOUT_ERROR,
        "format": ErrorCode.VIDEO_STREAM_FORMAT_ERROR,
        "processing": ErrorCode.VIDEO_FRAME_PROCESSING_ERROR,
        "not_found": ErrorCode.VIDEO_STREAM_NOT_FOUND,
    }
    
    return VideoStreamException(
        message=f"摄像头 {camera_id} 视频流{error_type}失败",
        camera_id=camera_id,
        stream_url=stream_url,
        error_code=error_code_map.get(error_type, ErrorCode.VIDEO_STREAM_CONNECTION_ERROR),
        original_exception=original_exception
    )


def create_ai_analysis_exception(
    message: str,
    model: str = None,
    error_type: str = "analysis",
    original_exception: Exception = None
) -> AIAnalysisException:
    """创建AI分析异常"""
    error_code_map = {
        "api": ErrorCode.AI_API_ERROR,
        "timeout": ErrorCode.AI_API_TIMEOUT,
        "quota": ErrorCode.AI_API_QUOTA_EXCEEDED,
        "response": ErrorCode.AI_API_INVALID_RESPONSE,
        "analysis": ErrorCode.AI_ANALYSIS_ERROR,
        "model": ErrorCode.AI_MODEL_NOT_AVAILABLE,
    }
    
    return AIAnalysisException(
        message=message,
        model=model,
        error_code=error_code_map.get(error_type, ErrorCode.AI_ANALYSIS_ERROR),
        original_exception=original_exception
    ) 