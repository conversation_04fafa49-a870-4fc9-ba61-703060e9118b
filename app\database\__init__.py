"""
数据库模块初始化
"""

from .engine import async_engine
from .connection import DatabaseManager, get_database_manager, get_session

# 全局数据库管理器实例
_db_manager: DatabaseManager | None = None


async def get_database_manager_instance() -> DatabaseManager:
    """获取全局数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
        await _db_manager.initialize()
    return _db_manager


async def close_database():
    """关闭数据库连接"""
    global _db_manager
    if _db_manager is not None:
        await _db_manager.close()
        _db_manager = None


def require_database():
    """确保数据库已初始化的装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            if _db_manager is None:
                raise RuntimeError("数据库尚未初始化，请先调用 get_database_manager_instance()")
            return await func(*args, **kwargs)
        return wrapper
    return decorator


__all__ = [
    "async_engine",
    "DatabaseManager", 
    "get_database_manager",
    "get_session",
    "get_database_manager_instance",
    "close_database",
    "require_database",
] 