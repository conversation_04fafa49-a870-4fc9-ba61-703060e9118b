"""
摄像头数据模型
"""

from typing import List, Optional
from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime, 
    ForeignKey, DECIMAL, Enum as SQLEnum
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from enum import Enum
from .base import BaseModel


class CameraStatus(Enum):
    """摄像头状态枚举"""
    ONLINE = "online"
    OFFLINE = "offline"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class Camera(BaseModel):
    """摄像头模型"""
    
    __tablename__ = "cameras"
    
    # 基础信息
    name: Mapped[str] = mapped_column(String(100), nullable=False, comment="摄像头名称")
    code: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, comment="摄像头编码")
    
    # 门店关联
    store_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("stores.id"), 
        nullable=False, 
        comment="所属门店ID"
    )
    
    # 连接信息
    rtsp_url: Mapped[str] = mapped_column(String(500), nullable=False, comment="RTSP地址")
    username: Mapped[Optional[str]] = mapped_column(String(50), comment="用户名")
    password: Mapped[Optional[str]] = mapped_column(String(100), comment="密码")
    
    # 物理信息
    location: Mapped[Optional[str]] = mapped_column(String(100), comment="安装位置")
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), comment="IP地址")
    port: Mapped[Optional[int]] = mapped_column(Integer, comment="端口号")
    
    # 视频参数
    resolution: Mapped[Optional[str]] = mapped_column(String(20), comment="分辨率")
    fps: Mapped[Optional[int]] = mapped_column(Integer, comment="帧率")
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    is_online: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否在线")
    
    # 配置信息
    description: Mapped[Optional[str]] = mapped_column(Text, comment="摄像头描述")
    
    # 关系映射
    store: Mapped["Store"] = relationship("Store", back_populates="cameras")
    
    analysis_rules: Mapped[List["AnalysisRule"]] = relationship(
        "AnalysisRule", 
        back_populates="camera",
        cascade="all, delete-orphan"
    )
    
    alert_events: Mapped[List["AlertEvent"]] = relationship(
        "AlertEvent", 
        back_populates="camera"
    )
    
    def __repr__(self) -> str:
        return f"<Camera(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    @property
    def analysis_enabled(self) -> bool:
        """分析是否启用"""
        return self.is_active and self.is_online 