# 智能视频监控预警系统 - 需求规格说明书

**版本：** v1.1  
**创建日期：** 2024-12-19  
**更新日期：** 2024-12-19  
**项目代号：** IVMS (Intelligent Video Monitoring System)  
**文档类型：** 需求规格说明书

## 目录
1. [项目概述](#1-项目概述)
2. [业务需求](#2-业务需求)
3. [功能需求](#3-功能需求)
4. [非功能需求](#4-非功能需求)
5. [系统约束](#5-系统约束)
6. [接口需求](#6-接口需求)
7. [验收标准](#7-验收标准)

## 1. 项目概述

### 1.1 项目背景
线下自主KTV门店需要一套智能视频监控预警系统，传统人工监控效率低下、成本高昂。本项目旨在开发一个**专门的视频分析服务**，通过AI技术自动分析监控视频，检测异常行为并生成预警信息存储到数据库，无需Web管理界面。

**⚠️ 核心架构特点：** 系统采用**数据库配置驱动**架构，所有检测规则、异常行为定义、提示词模板均存储在数据库中，AI分析服务动态加载配置，实现灵活的检测策略管理。

### 1.2 项目目标
- **提升监控效率**：通过AI自动分析替代人工监控，提高异常发现率
- **降低运营成本**：减少人力成本，提高监控覆盖率
- **快速响应**：实时检测异常并生成预警，为处置争取时间
- **智能存档**：自动保存异常证据，支持后续分析和取证
- **场景适配**：支持KTV特定场景的异常行为检测
- **配置灵活**：基于数据库配置的动态检测规则管理

### 1.3 核心价值主张
提供一个**高效、准确、自动化**的视频分析服务，专注于异常检测和预警数据管理，与现有业务系统松耦合集成。

### 1.4 项目范围
- **包含**：视频流处理、AI异常检测、预警数据存储、消息通知
- **不包含**：Web管理界面、用户权限管理、报表统计功能

## 2. 业务需求

### 2.1 业务场景
- **主要场景**：KTV门店实时监控
- **监控区域**：大厅、走廊、包房、安全通道
- **关注行为**：打斗、摔倒、设备破坏、异常滞留、火灾风险

### 2.2 用户角色
- **系统集成商**：部署和配置系统
- **安保人员**：接收预警信息并处理
- **管理人员**：查看异常记录和统计

### 2.3 业务流程
```mermaid
graph TD
    A[视频流输入] --> B[实时分析处理]
    B --> C{检测到异常?}
    C -->|是| D[生成预警信息]
    C -->|否| E[继续监控]
    D --> F[存储到数据库]
    D --> G[发送通知消息]
    F --> H[保存证据文件]
    E --> B
```

## 3. 功能需求

### 3.1 视频流管理 (F001)
**优先级：高**

#### 功能描述
管理多路视频流的接入、处理和缓存。

#### 详细需求
- **F001.1** 支持RTSP视频流接入，同时支持80-100路视频流
- **F001.2** 支持本地视频文件处理（测试和回放）
- **F001.3** 视频流状态监控：在线/离线/异常
- **F001.4** 智能帧采样：根据分析需求优化帧率
- **F001.5** 视频流缓冲：维护滑动窗口用于分析
- **F001.6** 断线重连机制：自动恢复异常的视频流

#### 输入/输出
- **输入**：RTSP URL、本地视频文件路径
- **输出**：处理后的视频帧序列

### 3.2 AI异常检测 (F002)
**优先级：高**

#### 功能描述
**基于数据库配置驱动**的AI异常检测服务，动态加载检测规则、异常行为定义和提示词模板，使用多模态大模型分析视频内容。

#### 详细需求
- **F002.1** 集成通义千问Qwen-VL多模态大模型
- **F002.2** **配置驱动检测**：从数据库动态加载以下配置
  - `anomaly_behaviors`：异常行为定义（打斗、摔倒、设备破坏等）
  - `analysis_rules`：针对摄像头的分析规则配置
  - `rule_behavior_mappings`：规则与异常行为的映射关系
  - `prompt_templates`：动态提示词模板
- **F002.3** **分层配置加载**：
  - 摄像头专属配置 > 门店级配置 > 全局默认配置
  - 行为专用提示词 > 行为默认提示词 > 全局默认提示词
- **F002.4** **动态参数调整**：
  - 置信度阈值：支持全局、规则、行为三级阈值覆盖
  - 连续帧数要求：可配置检测敏感度
  - 时间窗口和冷却时间：防止重复预警
- **F002.5** **异常行为类型**（从数据库加载）：
  - 暴力行为（violence）：打斗、攻击
  - 意外事故（accident）：摔倒、碰撞
  - 安全威胁（security）：设备破坏、入侵
  - 火灾风险（fire）：烟雾、明火
  - 人群异常（crowd）：聚集、踩踏
- **F002.6** **智能提示词构建**：根据配置动态构建AI提示词
- **F002.7** **配置热更新**：支持不重启服务更新检测配置

#### 输入/输出
- **输入**：视频帧序列、摄像头ID（用于加载对应配置）
- **输出**：异常检测结果、置信度分数、触发规则ID、详细描述

### 3.3 预警数据管理 (F003)
**优先级：高**

#### 功能描述
管理异常检测结果，生成预警记录并存储到数据库。

#### 详细需求
- **F003.1** 预警信息生成：包含异常类型、位置、时间、描述等
- **F003.2** 数据持久化：将预警信息存储到MySQL数据库
- **F003.3** 证据文件管理：保存异常相关的视频片段和截图
- **F003.4** 预警状态跟踪：待处理、处理中、已解决、误报等
- **F003.5** 预警级别分类：紧急、高、中、低
- **F003.6** 关联数据维护：门店、摄像头、检测规则等关联信息

#### 输入/输出
- **输入**：异常检测结果、摄像头信息、时间戳
- **输出**：预警记录ID、存储确认

### 3.4 消息通知服务 (F004)
**优先级：中**

#### 功能描述
向外部系统推送预警消息，支持实时通知。

#### 详细需求
- **F004.1** WebSocket实时推送：向连接的客户端推送预警
- **F004.2** 消息队列支持：支持Redis/RabbitMQ等消息中间件
- **F004.3** 通知模板：支持自定义通知消息格式
- **F004.4** 通知过滤：根据预警级别和类型过滤通知
- **F004.5** 通知确认机制：跟踪通知发送状态

#### 输入/输出
- **输入**：预警信息、通知配置
- **输出**：通知发送状态

### 3.5 基础配置管理 (F005)
**优先级：中**

#### 功能描述
管理系统运行的基础配置参数和外部服务配置。

#### 详细需求
- **F005.1** 视频源配置：添加、删除、修改视频流配置
- **F005.2** API密钥管理：第三方服务API密钥的安全存储（`api_keys`表）
- **F005.3** 第三方服务配置：外部服务端点和认证配置（`third_party_services`表）
- **F005.4** 系统参数配置：分析间隔、缓冲时长等参数
- **F005.5** 文件存储配置：MinIO等对象存储服务配置
- **F005.6** 配置验证：配置参数的有效性验证

#### 输入/输出
- **输入**：配置参数、API密钥
- **输出**：配置更新确认

### 3.6 动态配置管理 (F006)
**优先级：高**

#### 功能描述
**核心功能**：管理AI检测的动态配置，实现配置驱动的异常检测架构。

#### 详细需求
- **F006.1** **异常行为管理**（`anomaly_behaviors`表）：
  - 行为定义：名称、代码、分类、描述
  - AI关键词：用于模型识别的关键词数组
  - 默认参数：严重等级、置信度阈值
  - 状态管理：启用/禁用、排序权重
- **F006.2** **分析规则管理**（`analysis_rules`表）：
  - 规则配置：名称、适用范围（全局/门店/摄像头）
  - 触发条件：置信度阈值、连续帧数、时间窗口
  - 冷却机制：避免重复预警的冷却时间
  - 优先级：多规则冲突时的处理优先级
- **F006.3** **规则行为映射**（`rule_behavior_mappings`表）：
  - 关联关系：规则ID与异常行为ID的多对多映射
  - 参数覆盖：针对特定映射的阈值和等级覆盖
  - 权重配置：行为在规则中的权重分配
  - 自定义参数：特定行为的个性化检测参数
- **F006.4** **提示词模板管理**（`prompt_templates`表）：
  - 模板层次：算法行为专用 > 行为默认 > 全局默认
  - 模板内容：支持变量替换的提示词模板
  - 模型参数：温度、最大令牌数、系统提示词
  - 性能统计：使用次数、成功率、响应时间
- **F006.5** **配置加载策略**：
  - 启动加载：系统启动时预加载所有配置到缓存
  - 按需加载：根据摄像头ID动态加载相关配置
  - 分层查找：按优先级查找最匹配的配置
  - 缓存管理：Redis缓存配置，支持TTL和失效策略
- **F006.6** **配置热更新**：
  - 变更监听：数据库配置变更自动触发更新
  - 增量更新：仅更新变更的配置项
  - 更新通知：通过WebSocket/MQ通知服务实例
  - 回滚机制：配置错误时支持快速回滚
- **F006.7** **配置验证**：
  - 参数校验：配置参数的类型和范围验证
  - 依赖检查：配置项之间的依赖关系验证
  - 完整性检查：确保必要配置项完整
  - 冲突检测：检测配置冲突并提供建议

#### 配置加载流程
```mermaid
graph TD
    A[系统启动] --> B[加载基础配置]
    B --> C[构建配置缓存]
    C --> D[摄像头连接]
    D --> E[查询摄像头配置]
    E --> F[加载分析规则]
    F --> G[加载行为映射]
    G --> H[构建提示词模板]
    H --> I[启动AI检测]
    J[配置变更] --> K[更新缓存]
    K --> L[通知服务实例]
    L --> M[热更新配置]
```

#### 输入/输出
- **输入**：摄像头ID、配置变更事件
- **输出**：完整配置对象、更新确认状态

## 4. 非功能需求

### 4.1 性能需求

#### 4.1.1 处理能力
- **视频流并发**：支持同时处理80-100路视频流
- **分析延迟**：单次异常检测延迟 < 30秒
- **系统响应**：API响应时间 < 2秒
- **吞吐量**：每小时处理 > 10万帧图像

#### 4.1.2 资源使用
- **CPU使用率**：正常运行时 < 80%
- **内存使用**：< 32GB（包含模型加载）
- **GPU使用率**：< 90%（AI推理）
- **存储空间**：日志和临时文件 < 100GB/天

### 4.2 可靠性需求

#### 4.2.1 可用性
- **系统可用性**：≥ 99.5%
- **故障恢复时间**：< 5分钟
- **数据完整性**：预警数据零丢失

#### 4.2.2 容错能力
- **视频流断连**：自动重连，最多尝试3次
- **AI服务异常**：降级处理，记录错误日志
- **数据库连接失败**：本地缓存，重连后同步

### 4.3 可扩展性需求

#### 4.3.1 水平扩展
- **多实例部署**：支持分布式部署
- **负载均衡**：支持视频流分发到多个分析节点
- **存储扩展**：支持分布式文件存储

#### 4.3.2 功能扩展
- **新检测类型**：易于添加新的异常行为检测
- **模型升级**：支持AI模型的版本升级
- **接口扩展**：预留扩展接口

### 4.4 安全需求

#### 4.4.1 数据安全
- **敏感数据加密**：API密钥、数据库密码加密存储
- **传输加密**：HTTPS/WSS协议
- **访问控制**：基于IP白名单的访问限制

#### 4.4.2 系统安全
- **输入验证**：严格的参数校验
- **日志审计**：完整的操作日志记录
- **错误处理**：不暴露敏感的系统信息

### 4.5 可维护性需求

#### 4.5.1 监控和日志
- **系统监控**：CPU、内存、GPU使用率监控
- **业务监控**：处理速度、错误率、预警数量统计
- **日志管理**：结构化日志，支持日志轮转

#### 4.5.2 部署和配置
- **容器化部署**：支持Docker容器部署
- **配置管理**：配置文件和环境变量管理
- **版本管理**：支持版本回滚

## 5. 系统约束

### 5.1 技术约束
- **编程语言**：Python 3.8+
- **数据库**：MySQL 8.0+
- **操作系统**：Linux (Ubuntu 20.04+)
- **硬件要求**：RTX 4090 24GB GPU（生产环境）

### 5.2 外部依赖
- **通义千问API**：阿里云通义千问Qwen-VL模型服务
- **对象存储**：MinIO或兼容S3的存储服务
- **消息队列**：Redis或RabbitMQ（可选）

### 5.3 合规约束
- **数据保护**：符合个人信息保护相关法规
- **存储期限**：异常视频保存不超过30天
- **访问权限**：严格控制敏感数据访问权限

## 6. 接口需求

### 6.1 外部接口

#### 6.1.1 通义千问API接口
- **接口类型**：RESTful API
- **协议**：HTTPS
- **数据格式**：JSON
- **认证方式**：API Key

#### 6.1.2 对象存储接口
- **接口类型**：S3兼容API
- **协议**：HTTPS
- **认证方式**：Access Key + Secret Key

### 6.2 内部接口

#### 6.2.1 WebSocket接口
- **预警推送**：`ws://host:port/alerts`
- **数据格式**：JSON
- **消息类型**：预警通知、状态更新

#### 6.2.2 RESTful API接口
- **健康检查**：`GET /health`
- **配置管理**：`GET/PUT /api/config`
- **预警查询**：`GET /api/alerts`

### 6.3 数据库接口
- **连接方式**：MySQL连接池
- **事务支持**：支持数据库事务
- **表结构**：基于database_design.sql设计

## 7. 验收标准

### 7.1 功能验收标准

#### 7.1.1 视频流处理
- [ ] 支持80路RTSP视频流同时接入
- [ ] 视频流断线自动重连成功率 > 95%
- [ ] 帧采样和缓冲机制正常工作

#### 7.1.2 异常检测
- [ ] 异常检测准确率 ≥ 85%
- [ ] 误报率 ≤ 5%
- [ ] 支持至少5种异常行为类型检测

#### 7.1.3 数据存储
- [ ] 预警信息完整存储到数据库
- [ ] 证据文件正确保存到对象存储
- [ ] 数据库操作成功率 > 99.9%

#### 7.1.4 消息通知
- [ ] WebSocket消息推送成功率 > 99%
- [ ] 通知延迟 < 5秒

### 7.2 性能验收标准

#### 7.2.1 处理性能
- [ ] 80路视频流处理无明显延迟
- [ ] 单次异常检测延迟 < 30秒
- [ ] 系统连续运行24小时无故障

#### 7.2.2 资源使用
- [ ] CPU使用率峰值 < 80%
- [ ] 内存使用量 < 32GB
- [ ] GPU使用率 < 90%

### 7.3 可靠性验收标准

#### 7.3.1 可用性测试
- [ ] 系统连续运行7天可用性 ≥ 99.5%
- [ ] 故障恢复时间 < 5分钟
- [ ] 数据零丢失

#### 7.3.2 压力测试
- [ ] 100路视频流压力测试通过
- [ ] 大量异常事件处理不丢失
- [ ] 长时间运行内存无泄漏

### 7.4 安全验收标准

#### 7.4.1 数据安全
- [ ] 敏感配置信息已加密存储
- [ ] 通信协议使用HTTPS/WSS
- [ ] 访问控制机制有效

#### 7.4.2 系统安全
- [ ] 输入参数验证完整
- [ ] 错误信息不泄露敏感数据
- [ ] 日志记录完整且安全

## 8. 项目里程碑

### 8.1 第一阶段：核心功能开发 (4周)
- 视频流处理模块
- AI异常检测模块
- 数据库存储模块
- 基础配置管理

### 8.2 第二阶段：系统集成优化 (3周)
- 消息通知服务
- 性能优化
- 错误处理和容错
- 监控和日志

### 8.3 第三阶段：测试和部署 (2周)
- 功能测试
- 性能测试
- 安全测试
- 生产环境部署

---

**文档修订历史**

| 版本 | 日期 | 修订内容 | 修订人 |
|------|------|----------|--------|
| v1.0 | 2024-12-19 | 初始版本创建 | 系统分析师 |
| v1.1 | 2024-12-19 | 修正需求规格说明书 | 系统分析师 |

**文档审批**

| 角色 | 姓名 | 签名 | 日期 |
|------|------|------|------|
| 项目经理 |  |  |  |
| 技术负责人 |  |  |  |
| 质量保证 |  |  |  | 