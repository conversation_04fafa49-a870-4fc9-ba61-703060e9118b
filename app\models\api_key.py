"""
API密钥数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime,
    BigInteger, Enum as SQLEnum
)
from enum import Enum
from .base import BaseModel


class ApiKeyStatus(Enum):
    """API密钥状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    SUSPENDED = "suspended"


class ApiKey(BaseModel):
    """API密钥模型"""
    
    __tablename__ = "api_keys"
    
    # 密钥基本信息
    key_name = Column(String(100), nullable=False, index=True, comment="密钥名称")
    api_provider = Column(String(50), nullable=False, index=True, comment="API提供商")
    api_key = Column(String(500), nullable=False, comment="API密钥")
    api_secret = Column(String(500), comment="API密钥秘钥")
    
    # 状态信息
    status = Column(
        SQLEnum(ApiKeyStatus), 
        default=ApiKeyStatus.ACTIVE, 
        nullable=False, 
        index=True,
        comment="密钥状态"
    )
    is_default = Column(Boolean, default=False, comment="是否为默认密钥")
    
    # 使用配置
    max_requests_per_minute = Column(Integer, default=60, comment="每分钟最大请求数")
    max_requests_per_day = Column(Integer, default=1000, comment="每天最大请求数")
    priority = Column(Integer, default=100, comment="优先级(数值越小优先级越高)")
    
    # 使用统计
    total_requests = Column(BigInteger, default=0, comment="总请求次数")
    successful_requests = Column(BigInteger, default=0, comment="成功请求次数")
    failed_requests = Column(BigInteger, default=0, comment="失败请求次数")
    last_used_at = Column(DateTime, comment="最后使用时间")
    
    # 时效信息
    expires_at = Column(DateTime, comment="过期时间")
    
    # 备注信息
    description = Column(Text, comment="密钥描述")
    tags = Column(String(200), comment="标签")
    
    def __repr__(self) -> str:
        return f"<ApiKey(id={self.id}, name={self.key_name}, provider={self.api_provider})>"
    
    @property
    def is_active(self) -> bool:
        """是否为活跃状态"""
        return self.status == ApiKeyStatus.ACTIVE
    
    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        if self.expires_at:
            from datetime import datetime
            return datetime.utcnow() > self.expires_at
        return False
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests > 0:
            return round((self.successful_requests / self.total_requests) * 100, 2)
        return 0.0
    
    @property
    def failure_rate(self) -> float:
        """失败率"""
        if self.total_requests > 0:
            return round((self.failed_requests / self.total_requests) * 100, 2)
        return 0.0
    
    @property
    def masked_api_key(self) -> str:
        """脱敏的API密钥"""
        if not self.api_key:
            return ""
        if len(self.api_key) <= 8:
            return "*" * len(self.api_key)
        return f"{self.api_key[:4]}{'*' * (len(self.api_key) - 8)}{self.api_key[-4:]}"
    
    @property
    def tags_list(self) -> list:
        """获取标签列表"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(",") if tag.strip()]
    
    def set_tags(self, tags: list) -> None:
        """设置标签"""
        self.tags = ",".join(tags) if tags else None
    
    def increment_request_count(self, success: bool = True) -> None:
        """增加请求计数"""
        self.total_requests = (self.total_requests or 0) + 1
        if success:
            self.successful_requests = (self.successful_requests or 0) + 1
        else:
            self.failed_requests = (self.failed_requests or 0) + 1
    
    def update_last_used(self) -> None:
        """更新最后使用时间"""
        from datetime import datetime
        self.last_used_at = datetime.utcnow()
    
    def check_rate_limit(self, current_requests_per_minute: int, 
                        current_requests_per_day: int) -> bool:
        """检查速率限制"""
        if current_requests_per_minute >= self.max_requests_per_minute:
            return False
        if current_requests_per_day >= self.max_requests_per_day:
            return False
        return True
    
    def can_be_used(self) -> bool:
        """是否可以使用"""
        return self.is_active and not self.is_expired 