# 视频的预警分析服务

## 1. 项目概述

### 1.1 背景
本项目为线下自主KTV门店定制开发一套智能视频监控预警系统。传统视频监控依赖大量人工实时观看或事后回溯，效率低下、易疲劳、遗漏率高。特别在KTV等需要大范围、长时间监控的场所，人力成本高昂且效果难以保证。人工智能技术，特别是多模态大模型的发展，为视频内容的自动理解和分析提供了新的可能性。

### 1.2 目标
- **提升监控效率**：通过AI自动分析，减少人工盯屏时间，快速发现异常事件
- **提高预警准确性**：利用多模态模型的理解能力，结合自定义提示词，精准识别特定场景下的异常行为
- **加快响应速度**：实现异常事件的实时检测和即时推送，为安保人员争取宝贵的处置时间
- **智能化证据管理**：自动保存异常相关的视频片段和信息，便于事后追溯、分析和取证
- **场景适应性**：支持用户自定义提示词，使系统能灵活适应不同监控场景和关注点

### 1.3 核心价值
本系统旨在为KTV安防领域提供一套智能化、自动化、高效率的视频监控解决方案，降低人力成本，提升安全保障水平，特别关注打斗、人员摔倒、设备破坏、异常滞留等异常行为的自动检测。

### 1.4 应用场景
- KTV大厅、走廊和包房的安全监控
- 异常行为（打斗、摔倒、设备破坏、异常滞留）的实时检测
- 安全事件的自动记录与存档
- 管理端预警信息的实时推送

## 2. 系统架构

### 2.1 整体架构图

```mermaid
graph TD
    A[视频源管理] --> B[视频处理模块]
    B --> C[帧调度器]
    C --> D[异常检测模块]
    D --> E[通义千问分析]
    E --> F{异常判断}
    F -->|异常| G[预警服务模块]
    F -->|正常| H[普通记录]
    G --> I[WebSocket推送]
    G --> J[数据存储模块]
    J --> K[MinIO存储]
    J --> L[SQLite数据库]
```

### 2.2 核心流程

#### 2.2.1 视频处理流程
```mermaid
graph LR
    A[视频输入源] --> B[视频处理器]
    B -->|视频帧| C[帧缓冲区]
    C -->|批量帧| D[多模态分析器]
    D -->|分析| E[内容描述生成]
    E -->|描述文本| F[异常行为检测]
    F -->|检测结果| G{异常判断}
```

#### 2.2.2 预警服务流程
```mermaid
graph TB
    G{异常判断} -->|是| H[预警服务]
    G -->|否| I[历史记录存储]
    
    H -->|WebSocket| J[客户端推送]
    H -->|触发| K[异常视频保存]
    H -->|触发| L[异常截图保存]
    
    I -->|定期| M[历史内容总结]
```

### 2.3 主要组件
- **视频管理器(VideoManager)**：负责视频源的管理和处理器的创建
- **视频处理器(VideoProcessor)**：处理单路视频流，提取帧
- **帧调度器(FrameScheduler)**：根据优先级调度视频帧的处理顺序
- **异常检测器(AnomalyDetector)**：分析视频帧，检测异常行为
- **通义千问客户端(QwenClient)**：调用大模型API进行视频内容分析
- **预警服务(AlertService)**：处理检测到的异常，推送预警信息
- **数据库(Database)**：存储异常记录和系统配置
- **存储服务(StorageService)**：管理视频片段和截图的存储

## 3. 关键模块

### 3.1 视频处理模块
- **功能**：接收视频流，处理视频帧，维护帧缓冲区
- **核心组件**：
  - 视频管理器：管理多路视频源
  - 视频处理器：处理单路视频流
  - 帧缓冲区：存储待分析的视频帧
- **技术实现**：
  - 使用OpenCV+GStreamer处理RTSP流
  - 支持同时处理80-100路视频流
  - 智能帧采样以优化处理性能

### 3.2 异常检测模块
- **功能**：分析视频帧，检测异常行为
- **核心组件**：
  - 异常检测器：协调分析和检测流程
  - 通义千问客户端：调用大模型API
  - 误报过滤机制：减少误报
- **技术实现**：
  - 使用阿里云通义千问Qwen-VL多模态大模型
  - 实现持续性检测机制，减少误报
  - 支持场景自适应的提示词生成

### 3.3 预警服务模块
- **功能**：处理检测到的异常，生成预警信息，推送给客户端
- **核心组件**：
  - 预警回调处理器：处理检测到的异常
  - WebSocket服务：推送预警信息
  - 预警记录管理：管理预警历史
- **技术实现**：
  - 使用WebSocket进行实时推送
  - 支持预警级别的定制
  - 提供预警确认和处理机制

### 3.4 数据存储模块
- **功能**：存储异常记录、视频片段和截图
- **核心组件**：
  - 数据库接口：提供数据持久化服务
  - 存储服务：管理文件存储
- **技术实现**：
  - 使用SQLite存储结构化数据
  - 使用MinIO存储视频片段和截图
  - 实现数据备份和清理机制

## 4. 技术栈

### 4.1 硬件要求
- 一台RTX 4090 24GB GPU服务器：用于部署本地大模型（生产环境）
- 一台应用服务器：部署应用服务

### 4.2 软件依赖
- **编程语言**：Python
- **视频处理**：OpenCV, GStreamer
- **Web框架**：FastAPI
- **异步处理**：asyncio
- **数据库**：SQLite
- **对象存储**：MinIO
- **容器化**：Docker (可选)

### 4.3 第三方服务
- **大模型服务**：阿里云通义千问API
  - QWEN_API_KEY = ""
  - QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
  - QWEN_MODEL = "qwen-vl-max-2025-01-25"
- **对象存储**：MinIO
  - S3_ENDPOINT = "http://124.71.23.144:9000"
  - S3_REGION = "us-east-1"
  - S3_BUCKET_NAME = "difybn"
  - S3_ACCESS_KEY = "v3Di4s9aszZC6DzeNqHa"
  - S3_SECRET_KEY = "YAXnJlXazQsjPvsJhZk9ow3qRfvl8Gglu7P8OpHJ"

## 5. 开发路线图

### 5.1 第一阶段：技术验证与核心链路打通
**目标**：建立最小可行系统，验证核心流程可行性

1. **技术选型验证**
   - 视频处理框架：OpenCV + GStreamer（测试RTSP流处理能力）
   - 模型推理测试：使用通义API测试不同分辨率/帧率下的响应时间和准确率
   - 编写技术验证脚本

2. **核心链路实现**
   - 实现端到端处理流程：视频输入 → 帧抽取 → 模型推理 → 结果解析 → 日志输出
   - 开发核心Pipeline类

3. **异常检测原型**
   - 实现基础规则引擎
   - 开发初步的异常检测逻辑

4. **交付物验收**
   - 单路视频处理Demo
   - 基础异常检测演示
   - 技术可行性验证报告

### 5.2 第二阶段：多路处理与工程化
**目标**：实现80路视频并发处理基础框架

1. **视频管理服务**
   - 实现视频源管理器
   - 支持动态增删视频源

2. **资源调度优化**
   - 实现智能帧调度算法
   - 开发GPU内存监控模块

3. **分布式处理框架**
   - 构建生产者-消费者模型
   - 使用Redis实现任务队列

4. **交付物验收**
   - 支持80路视频接入
   - GPU利用率监控面板
   - 负载均衡演示

### 5.3 第三阶段：智能分析强化
**目标**：提升异常检测准确率与场景适应性

1. **多模态融合**
   - 开发视觉特征提取器
   - 构建时空特征金字塔

2. **动态提示词引擎**
   - 实现提示词模板系统
   - 支持场景自适应配置

3. **误报过滤机制**
   - 实现多模型投票机制
   - 优化检测置信度阈值

4. **交付物验收**
   - 异常检测准确率≥85%
   - 支持场景自适应配置
   - 误报率≤5%测试报告

## 6. 接口设计

### 6.1 WebSocket接口
- **预警消息WebSocket**：`ws://localhost:16532/alerts`
  - 功能：实时推送预警信息
  - 数据格式：JSON
  - 字段：
    ```json
    {
      "id": "告警ID",
      "source_id": "视频源ID",
      "source_name": "视频源名称",
      "location": "位置信息",
      "type": "异常类型",
      "summary": "简要描述",
      "description": "详细描述",
      "confidence": 0.85,
      "image_path": "截图路径",
      "video_path": "视频片段路径",
      "timestamp": "2023-10-01T12:00:00Z"
    }
    ```

- **视频流WebSocket**：`ws://localhost:16532/video_feed`
  - 功能：实时推送视频流
  - 数据格式：二进制

### 6.2 REST API接口
- **视频源管理API**
  - `GET /api/sources`：获取所有视频源
  - `POST /api/sources`：添加新视频源
  - `DELETE /api/sources/{id}`：删除视频源
  - `PUT /api/sources/{id}`：更新视频源

- **异常记录API**
  - `GET /api/alerts`：获取预警记录
  - `GET /api/alerts/{id}`：获取特定预警详情
  - `PUT /api/alerts/{id}/status`：更新预警状态

- **系统配置API**
  - `GET /api/config`：获取系统配置
  - `PUT /api/config`：更新系统配置

## 7. 部署与维护

### 7.1 部署要求
- **硬件**：
  - RTX 4090 24GB GPU服务器
  - 应用服务器
  - 网络环境：支持RTSP流

- **软件**：
  - 操作系统：Linux (推荐Ubuntu 20.04+)
  - Docker (可选)
  - Python 3.8+
  - CUDA 11.7+ (GPU服务器)

### 7.2 配置说明
- **环境变量配置**：
  - 通义千问API配置
  - MinIO存储配置
  - 系统参数配置

- **启动命令**：
  ```bash
  # 使用本地视频文件
  python xxxx.py --video_source "./测试视频/xxx.mp4"

  # 或使用RTSP视频流
  python xxxx.py --video_source "rtsp://xxx.xxx.xxx.xxx/xxx"
  ```

### 7.3 监控与维护
- **日志系统**：
  - 系统日志：`code.log`
  - 异常记录：数据库存储

- **性能监控**：
  - GPU使用率监控
  - 内存使用监控
  - API调用频率监控

- **数据备份**：
  - 定期备份SQLite数据库
  - 定期备份MinIO存储的重要数据

## 8. 异常检测器核心实现

异常检测器(AnomalyDetector)是系统的核心组件，负责分析视频帧并检测异常行为。其主要功能包括：

### 8.1 初始化与配置
- 设置检测间隔和置信度阈值
- 初始化通义千问客户端
- 设置误报过滤机制参数

### 8.2 检测循环
- 获取所有视频处理器
- 使用帧调度器对处理器进行优先级排序
- 从处理器获取帧缓冲区
- 分析帧并检测异常

### 8.3 误报过滤机制
- 实现持续性检测窗口(persistence_window)
- 设置确认异常所需的最小检测次数(persistence_threshold)
- 定期清理旧的潜在告警记录

### 8.4 异常处理流程
- 保存异常帧截图
- 记录异常信息到数据库
- 触发预警回调函数
- 推送预警信息

### 8.5 核心代码结构
```python
class AnomalyDetector:
    def __init__(self, interval=5.0, confidence_threshold=0.7):
        # 初始化配置
        
    def start(self):
        # 启动检测线程
        
    def stop(self):
        # 停止检测
        
    def _detection_loop(self):
        # 主检测循环
        
    def _analyze_frames(self, source_id, processor, frames):
        # 分析帧并检测异常
        
    def _check_anomaly_persistence(self, source_id, anomaly_type):
        # 检查异常持续性
``` 