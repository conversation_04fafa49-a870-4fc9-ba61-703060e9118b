"""
WebSocket路由 - 实时连接管理
"""
import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, Depends
from fastapi.security import HTTPBearer

from app.services.notification.websocket_service import websocket_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ws", tags=["WebSocket"])
security = HTTPBearer()


@router.websocket("/connect")
async def websocket_endpoint(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None, description="客户端ID"),
    client_type: Optional[str] = Query("web", description="客户端类型")
):
    """WebSocket连接端点"""
    client_info = {
        "client_id": client_id,
        "client_type": client_type,
        "user_agent": websocket.headers.get("user-agent", ""),
        "origin": websocket.headers.get("origin", "")
    }
    
    logger.info(f"WebSocket连接请求: {client_info}")
    
    try:
        await websocket_service.handle_websocket(websocket, client_info)
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {client_info}")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {str(e)}")


@router.websocket("/alerts")
async def alerts_websocket(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None, description="客户端ID")
):
    """专用的预警WebSocket连接"""
    client_info = {
        "client_id": client_id,
        "client_type": "alerts_client",
        "subscription": "alerts"
    }
    
    logger.info(f"预警WebSocket连接请求: {client_info}")
    
    try:
        await websocket_service.handle_websocket(websocket, client_info)
        
        # 自动订阅预警消息
        await websocket_service.manager.subscribe(websocket, "alerts")
        
    except WebSocketDisconnect:
        logger.info(f"预警WebSocket连接断开: {client_info}")
    except Exception as e:
        logger.error(f"预警WebSocket连接异常: {str(e)}")


@router.websocket("/camera/{camera_id}")
async def camera_websocket(
    websocket: WebSocket,
    camera_id: int,
    client_id: Optional[str] = Query(None, description="客户端ID")
):
    """摄像头专用WebSocket连接"""
    client_info = {
        "client_id": client_id,
        "client_type": "camera_client",
        "camera_id": camera_id,
        "subscription": f"camera_{camera_id}"
    }
    
    logger.info(f"摄像头WebSocket连接请求: {client_info}")
    
    try:
        await websocket_service.handle_websocket(websocket, client_info)
        
        # 自动订阅摄像头状态消息
        await websocket_service.manager.subscribe(websocket, "camera_status")
        await websocket_service.manager.subscribe(websocket, f"camera_{camera_id}")
        
    except WebSocketDisconnect:
        logger.info(f"摄像头WebSocket连接断开: {client_info}")
    except Exception as e:
        logger.error(f"摄像头WebSocket连接异常: {str(e)}")


@router.websocket("/monitor")
async def monitor_websocket(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None, description="客户端ID")
):
    """系统监控WebSocket连接"""
    client_info = {
        "client_id": client_id,
        "client_type": "monitor_client",
        "subscription": "system_monitor"
    }
    
    logger.info(f"监控WebSocket连接请求: {client_info}")
    
    try:
        await websocket_service.handle_websocket(websocket, client_info)
        
        # 自动订阅系统状态消息
        await websocket_service.manager.subscribe(websocket, "system_status")
        await websocket_service.manager.subscribe(websocket, "camera_status")
        
    except WebSocketDisconnect:
        logger.info(f"监控WebSocket连接断开: {client_info}")
    except Exception as e:
        logger.error(f"监控WebSocket连接异常: {str(e)}") 