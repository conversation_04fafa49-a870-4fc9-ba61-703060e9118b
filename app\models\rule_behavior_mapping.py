"""
规则行为映射数据模型
"""

from sqlalchemy import (
    Column, Integer, Boolean, ForeignKey, 
    DECIMAL, JSON, UniqueConstraint
)
from sqlalchemy.orm import relationship
from .base import BaseModel


class RuleBehaviorMapping(BaseModel):
    """规则行为映射模型"""
    
    __tablename__ = "rule_behavior_mappings"
    __table_args__ = (
        UniqueConstraint('analysis_rule_id', 'anomaly_behavior_id', name='uq_rule_behavior'),
    )
    
    # 关联信息
    analysis_rule_id = Column(
        Integer, 
        ForeignKey("analysis_rules.id"), 
        nullable=False, 
        index=True, 
        comment="分析规则ID"
    )
    anomaly_behavior_id = Column(
        Integer, 
        ForeignKey("anomaly_behaviors.id"), 
        nullable=False, 
        index=True, 
        comment="异常行为ID"
    )
    
    # 映射配置
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 个性化参数（可覆盖默认值）
    custom_confidence_threshold = Column(DECIMAL(3, 2), comment="自定义置信度阈值")
    custom_continuous_frames = Column(Integer, comment="自定义连续帧数")
    custom_time_window = Column(Integer, comment="自定义时间窗口")
    custom_cooldown_period = Column(Integer, comment="自定义冷却期")
    
    # 扩展配置
    custom_config = Column(JSON, comment="自定义配置JSON")
    
    # 关联关系
    analysis_rule = relationship("AnalysisRule", back_populates="behavior_mappings")
    anomaly_behavior = relationship("AnomalyBehavior", back_populates="rule_mappings")
    
    def __repr__(self) -> str:
        return f"<RuleBehaviorMapping(id={self.id}, rule_id={self.analysis_rule_id}, behavior_id={self.anomaly_behavior_id})>"
    
    def get_effective_confidence_threshold(self) -> float:
        """获取有效的置信度阈值"""
        if self.custom_confidence_threshold is not None:
            return float(self.custom_confidence_threshold)
        if self.anomaly_behavior and self.anomaly_behavior.default_confidence_threshold:
            return float(self.anomaly_behavior.default_confidence_threshold)
        return 0.7  # 默认值
    
    def get_effective_continuous_frames(self) -> int:
        """获取有效的连续帧数"""
        if self.custom_continuous_frames is not None:
            return self.custom_continuous_frames
        if self.analysis_rule and self.analysis_rule.continuous_frames:
            return self.analysis_rule.continuous_frames
        return 3  # 默认值
    
    def get_effective_time_window(self) -> int:
        """获取有效的时间窗口"""
        if self.custom_time_window is not None:
            return self.custom_time_window
        if self.analysis_rule and self.analysis_rule.time_window:
            return self.analysis_rule.time_window
        return 30  # 默认值
    
    def get_effective_cooldown_period(self) -> int:
        """获取有效的冷却期"""
        if self.custom_cooldown_period is not None:
            return self.custom_cooldown_period
        if self.analysis_rule and self.analysis_rule.cooldown_period:
            return self.analysis_rule.cooldown_period
        return 60  # 默认值
    
    def get_custom_config(self, key: str, default=None):
        """获取自定义配置"""
        if not self.custom_config:
            return default
        return self.custom_config.get(key, default)
    
    @property
    def behavior_name(self) -> str:
        """获取行为名称"""
        return self.anomaly_behavior.behavior_name if self.anomaly_behavior else ""
    
    @property
    def rule_name(self) -> str:
        """获取规则名称"""
        return self.analysis_rule.rule_name if self.analysis_rule else "" 