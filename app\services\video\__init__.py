"""
视频流管理服务
支持80-100路视频流并发处理
包含三层优化架构：预过滤层、聚合层、AI分析层
"""

from .video_manager import VideoManager
from .video_processor import VideoProcessor
from .frame_scheduler import FrameScheduler
from .frame_buffer import Frame<PERSON>uffer, FrameBufferManager, VideoFrame
from .motion_detector import (
    MotionDetector, 
    MotionDetectionManager, 
    MotionDetectionConfig,
    MotionDetectionResult, 
    MotionDetectionAlgorithm
)
from .pre_filter import (
    PreFilterProcessor,
    PreFilterConfig,
    PreFilterResult,
    PreFilterStatistics,
    FilterDecision,
    FilterDecisionType,
    FrameSimilarityAnalyzer
)
from .frame_aggregator import (
    FrameAggregator,
    FrameGroup,
    AggregationStrategy,
    AggregationDecision,
    AggregationResult
)
from .ai_integration import (
    AIIntegrationService,
    AIAnalysisQueue,
    AnalysisRequest,
    AnalysisResponse,
    AIIntegrationStatistics
)
from .video_pipeline import (
    VideoPipeline,
    PipelineStatus,
    PipelineConfig,
    PipelineStatistics
)

__all__ = [
    # 核心管理器
    "VideoManager",
    "VideoProcessor", 
    "FrameScheduler",
    
    # 帧缓冲区
    "FrameBuffer",
    "FrameBufferManager",
    "VideoFrame",
    
    # 运动检测
    "MotionDetector",
    "MotionDetectionManager", 
    "MotionDetectionConfig",
    "MotionDetectionResult",
    "MotionDetectionAlgorithm",
    
    # 预过滤层（第一层优化）
    "PreFilterProcessor",
    "PreFilterConfig",
    "PreFilterResult",
    "PreFilterStatistics",
    "FilterDecision",
    "FilterDecisionType",
    "FrameSimilarityAnalyzer",
    
    # 聚合层（第二层优化）
    "FrameAggregator",
    "FrameGroup",
    "AggregationStrategy",
    "AggregationDecision",
    "AggregationResult",
    
    # AI分析层（第三层优化）
    "AIIntegrationService",
    "AIAnalysisQueue",
    "AnalysisRequest",
    "AnalysisResponse",
    "AIIntegrationStatistics",
    
    # 视频处理管道
    "VideoPipeline",
    "PipelineStatus",
    "PipelineConfig", 
    "PipelineStatistics"
] 