"""
证据文件数据访问层
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..database.base_repository import BaseRepository
from ..models.evidence_file import EvidenceFile
from ..core.logger import logger


class EvidenceFileRepository(BaseRepository[EvidenceFile]):
    """证据文件Repository"""
    
    def __init__(self, db: Session):
        super().__init__(db, EvidenceFile)
    
    async def get_alert_evidences(self, alert_id: int) -> List[EvidenceFile]:
        """获取预警的所有证据文件"""
        return await self.find_by_conditions([
            EvidenceFile.alert_id == alert_id,
            EvidenceFile.deleted == False
        ], order_by=[EvidenceFile.capture_time.asc()])
    
    async def get_key_evidences(self, alert_id: int) -> List[EvidenceFile]:
        """获取预警的关键证据文件"""
        return await self.find_by_conditions([
            EvidenceFile.alert_id == alert_id,
            EvidenceFile.is_key_evidence == True,
            EvidenceFile.deleted == False
        ], order_by=[EvidenceFile.capture_time.asc()])
    
    async def create_evidence(self, evidence_data: Dict[str, Any]) -> Optional[int]:
        """创建证据文件记录"""
        try:
            evidence_id = await self.create(evidence_data)
            if evidence_id:
                logger.info(f"创建证据文件: {evidence_id}")
            return evidence_id
            
        except Exception as e:
            logger.error(f"创建证据文件失败: {e}")
            return None 