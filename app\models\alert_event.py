"""
预警事件数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime,
    ForeignKey, DECIMAL, Enum as SQLEnum
)
from sqlalchemy.orm import relationship
from enum import Enum
from .base import BaseModel


class AlertStatus(Enum):
    """预警状态枚举"""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    IGNORED = "ignored"
    RESOLVED = "resolved"


class AlertEvent(BaseModel):
    """预警事件模型"""
    
    __tablename__ = "alert_events"
    
    # 关联信息
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False, index=True, comment="所属门店ID")
    camera_id = Column(Integer, ForeignKey("cameras.id"), nullable=False, index=True, comment="摄像头ID")
    anomaly_behavior_id = Column(Integer, ForeignKey("anomaly_behaviors.id"), nullable=False, index=True, comment="异常行为ID")
    analysis_rule_id = Column(Integer, ForeignKey("analysis_rules.id"), nullable=False, index=True, comment="触发规则ID")
    
    # 预警基本信息
    alert_title = Column(String(200), nullable=False, comment="预警标题")
    alert_description = Column(Text, comment="预警描述")
    alert_level = Column(String(20), nullable=False, comment="预警级别")
    alert_time = Column(DateTime, nullable=False, index=True, comment="预警时间")
    
    # AI分析结果
    ai_analysis_result = Column(Text, comment="AI分析结果")
    confidence_score = Column(DECIMAL(5, 4), comment="置信度分数")
    detected_objects = Column(Text, comment="检测到的对象")
    
    # 状态信息
    status = Column(
        SQLEnum(AlertStatus), 
        default=AlertStatus.PENDING, 
        nullable=False, 
        index=True,
        comment="处理状态"
    )
    
    # 处理信息
    processed_at = Column(DateTime, comment="处理时间")
    processed_by = Column(String(100), comment="处理人")
    process_notes = Column(Text, comment="处理备注")
    
    # 通知状态
    notification_sent = Column(Boolean, default=False, comment="是否已发送通知")
    notification_channels = Column(String(200), comment="通知渠道")
    notification_time = Column(DateTime, comment="通知时间")
    
    # 关联关系
    store = relationship("Store", back_populates="alert_events")
    camera = relationship("Camera", back_populates="alert_events")
    anomaly_behavior = relationship("AnomalyBehavior", back_populates="alert_events")
    analysis_rule = relationship("AnalysisRule", back_populates="alert_events")
    evidence_files = relationship("EvidenceFile", back_populates="alert_event", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<AlertEvent(id={self.id}, title={self.alert_title}, level={self.alert_level})>"
    
    @property
    def is_pending(self) -> bool:
        """是否为待处理状态"""
        return self.status == AlertStatus.PENDING
    
    @property
    def is_confirmed(self) -> bool:
        """是否已确认"""
        return self.status == AlertStatus.CONFIRMED
    
    @property
    def is_resolved(self) -> bool:
        """是否已解决"""
        return self.status == AlertStatus.RESOLVED
    
    @property
    def is_ignored(self) -> bool:
        """是否被忽略"""
        return self.status == AlertStatus.IGNORED
    
    @property
    def severity_level(self) -> str:
        """获取严重程度"""
        if self.anomaly_behavior:
            return self.anomaly_behavior.severity_level.value
        return "medium"
    
    @property
    def evidence_count(self) -> int:
        """证据文件数量"""
        return len(self.evidence_files)
    
    @property
    def has_evidence(self) -> bool:
        """是否有证据文件"""
        return self.evidence_count > 0
    
    def get_detected_objects_list(self) -> list:
        """获取检测对象列表"""
        if not self.detected_objects:
            return []
        return [obj.strip() for obj in self.detected_objects.split(",") if obj.strip()]
    
    def set_detected_objects(self, objects: list) -> None:
        """设置检测对象"""
        self.detected_objects = ",".join(objects) if objects else None
    
    def mark_as_processed(self, processor: str, notes: str = None) -> None:
        """标记为已处理"""
        from datetime import datetime
        self.processed_at = datetime.utcnow()
        self.processed_by = processor
        if notes:
            self.process_notes = notes
    
    def mark_notification_sent(self, channels: list = None) -> None:
        """标记通知已发送"""
        from datetime import datetime
        self.notification_sent = True
        self.notification_time = datetime.utcnow()
        if channels:
            self.notification_channels = ",".join(channels) 