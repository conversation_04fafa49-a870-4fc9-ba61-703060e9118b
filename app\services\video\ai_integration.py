"""
AI分析集成器 - 三层优化架构的第三层

主要功能：
1. 将AI分析器集成到视频处理流程
2. 支持单帧和批量分析
3. 异常行为检测和预警生成
4. 结果处理和存储
5. 性能监控和优化
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from threading import Lock
import json

from .frame_buffer import VideoFrame
from .pre_filter import PreFilterResult, FilterDecision
from ..ai.ai_analyzer import AIAnalyzer, FrameData, AnalysisResult
from ..alert.alert_service import AlertService, AlertEvent
from ..storage.file_storage_service import FileStorageService

logger = logging.getLogger(__name__)


class AnalysisMode(Enum):
    """分析模式"""
    SINGLE_FRAME = "single_frame"  # 单帧分析
    BATCH_ANALYSIS = "batch_analysis"  # 批量分析
    CONTINUOUS_ANALYSIS = "continuous_analysis"  # 连续分析
    PRIORITY_ANALYSIS = "priority_analysis"  # 优先级分析


@dataclass
class AnalysisRequest:
    """分析请求"""
    frame: VideoFrame
    filter_result: PreFilterResult
    priority: int = 0  # 优先级 (0=最高)
    analysis_mode: AnalysisMode = AnalysisMode.SINGLE_FRAME
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """设置分析模式"""
        if self.filter_result.decision == FilterDecision.HIGH_PRIORITY:
            self.analysis_mode = AnalysisMode.PRIORITY_ANALYSIS
            self.priority = 0
        elif self.filter_result.decision == FilterDecision.BATCH_PROCESS:
            self.analysis_mode = AnalysisMode.BATCH_ANALYSIS
            self.priority = 2
        else:
            self.analysis_mode = AnalysisMode.SINGLE_FRAME
            self.priority = 1


@dataclass
class AnalysisResponse:
    """分析响应"""
    request: AnalysisRequest
    analysis_result: Optional[AnalysisResult]
    processing_time_ms: float
    success: bool
    error_message: Optional[str] = None
    generated_alerts: List[AlertEvent] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BatchAnalysisRequest:
    """批量分析请求"""
    frames: List[VideoFrame]
    filter_results: List[PreFilterResult]
    source_id: str
    batch_id: str
    created_at: float = field(default_factory=time.time)


@dataclass
class AIIntegrationStatistics:
    """AI集成统计"""
    total_requests: int = 0
    successful_analyses: int = 0
    failed_analyses: int = 0
    single_frame_analyses: int = 0
    batch_analyses: int = 0
    priority_analyses: int = 0
    
    total_processing_time_ms: float = 0.0
    avg_processing_time_ms: float = 0.0
    
    alerts_generated: int = 0
    anomalies_detected: int = 0
    
    cost_savings: float = 0.0  # 基于预过滤的成本节省
    api_calls_saved: int = 0
    
    performance_score: float = 0.0


class AIAnalysisQueue:
    """AI分析队列管理器"""
    
    def __init__(self, max_queue_size: int = 1000):
        self.max_queue_size = max_queue_size
        
        # 分优先级的队列
        self._priority_queue: List[AnalysisRequest] = []
        self._normal_queue: List[AnalysisRequest] = []
        self._batch_queue: List[AnalysisRequest] = []
        
        # 批量处理缓冲
        self._batch_buffers: Dict[str, List[AnalysisRequest]] = {}
        
        # 锁
        self._lock = Lock()
        
        logger.info("AI分析队列管理器已初始化")
    
    def add_request(self, request: AnalysisRequest) -> bool:
        """添加分析请求"""
        with self._lock:
            # 检查队列大小
            total_size = len(self._priority_queue) + len(self._normal_queue) + len(self._batch_queue)
            if total_size >= self.max_queue_size:
                logger.warning("AI分析队列已满，丢弃请求")
                return False
            
            # 根据分析模式分配到不同队列
            if request.analysis_mode == AnalysisMode.PRIORITY_ANALYSIS:
                self._priority_queue.append(request)
                self._priority_queue.sort(key=lambda x: x.priority)
            elif request.analysis_mode == AnalysisMode.BATCH_ANALYSIS:
                self._add_to_batch_buffer(request)
            else:
                self._normal_queue.append(request)
                self._normal_queue.sort(key=lambda x: x.priority)
            
            return True
    
    def _add_to_batch_buffer(self, request: AnalysisRequest):
        """添加到批量处理缓冲区"""
        source_id = request.frame.source_id
        if source_id not in self._batch_buffers:
            self._batch_buffers[source_id] = []
        
        self._batch_buffers[source_id].append(request)
        
        # 如果缓冲区达到批量大小，移到批量队列
        if len(self._batch_buffers[source_id]) >= 5:  # 批量大小
            self._batch_queue.extend(self._batch_buffers[source_id])
            self._batch_buffers[source_id] = []
    
    def get_next_request(self) -> Optional[AnalysisRequest]:
        """获取下一个分析请求"""
        with self._lock:
            # 优先级队列优先
            if self._priority_queue:
                return self._priority_queue.pop(0)
            
            # 然后是普通队列
            if self._normal_queue:
                return self._normal_queue.pop(0)
            
            # 最后是批量队列
            if self._batch_queue:
                return self._batch_queue.pop(0)
            
            return None
    
    def get_batch_requests(self, max_batch_size: int = 5) -> List[AnalysisRequest]:
        """获取批量请求"""
        with self._lock:
            batch = []
            
            # 从批量队列中获取同一来源的请求
            if self._batch_queue:
                source_id = self._batch_queue[0].frame.source_id
                
                for i in range(len(self._batch_queue)):
                    if (len(batch) < max_batch_size and 
                        self._batch_queue[i].frame.source_id == source_id):
                        batch.append(self._batch_queue.pop(i))
                        break
            
            return batch
    
    def get_queue_status(self) -> Dict[str, int]:
        """获取队列状态"""
        with self._lock:
            return {
                "priority_queue": len(self._priority_queue),
                "normal_queue": len(self._normal_queue),
                "batch_queue": len(self._batch_queue),
                "batch_buffers": sum(len(buf) for buf in self._batch_buffers.values())
            }
    
    def clear_old_requests(self, max_age_seconds: float = 30.0):
        """清理过期请求"""
        current_time = time.time()
        with self._lock:
            # 清理各个队列中的过期请求
            for queue in [self._priority_queue, self._normal_queue, self._batch_queue]:
                queue[:] = [req for req in queue 
                           if current_time - req.frame.timestamp <= max_age_seconds]
            
            # 清理批量缓冲区中的过期请求
            for source_id in list(self._batch_buffers.keys()):
                self._batch_buffers[source_id][:] = [
                    req for req in self._batch_buffers[source_id]
                    if current_time - req.frame.timestamp <= max_age_seconds
                ]
                
                if not self._batch_buffers[source_id]:
                    del self._batch_buffers[source_id]


class AIIntegrationService:
    """AI集成服务"""
    
    def __init__(
        self,
        ai_analyzer: AIAnalyzer,
        alert_service: AlertService,
        storage_service: FileStorageService,
        max_concurrent_analyses: int = 10,
        batch_size: int = 5,
        batch_timeout: float = 2.0
    ):
        self.ai_analyzer = ai_analyzer
        self.alert_service = alert_service
        self.storage_service = storage_service
        self.max_concurrent_analyses = max_concurrent_analyses
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        
        # 分析队列
        self._analysis_queue = AIAnalysisQueue()
        
        # 统计信息
        self._statistics: Dict[str, AIIntegrationStatistics] = {}
        self._global_statistics = AIIntegrationStatistics()
        
        # 工作器控制
        self._workers: List[asyncio.Task] = []
        self._running = False
        
        # 锁
        self._lock = Lock()
        
        logger.info("AI集成服务已初始化")
    
    async def start(self):
        """启动AI集成服务"""
        if self._running:
            logger.warning("AI集成服务已在运行")
            return
        
        self._running = True
        
        # 启动工作器
        for i in range(self.max_concurrent_analyses):
            worker = asyncio.create_task(self._analysis_worker(f"worker-{i}"))
            self._workers.append(worker)
        
        # 启动批量处理器
        batch_worker = asyncio.create_task(self._batch_processor())
        self._workers.append(batch_worker)
        
        # 启动清理器
        cleanup_worker = asyncio.create_task(self._cleanup_worker())
        self._workers.append(cleanup_worker)
        
        logger.info(f"AI集成服务已启动，工作器数量: {len(self._workers)}")
    
    async def stop(self):
        """停止AI集成服务"""
        if not self._running:
            return
        
        self._running = False
        
        # 取消所有工作器
        for worker in self._workers:
            worker.cancel()
        
        # 等待工作器完成
        await asyncio.gather(*self._workers, return_exceptions=True)
        self._workers.clear()
        
        logger.info("AI集成服务已停止")
    
    async def analyze_frame(
        self, 
        frame: VideoFrame, 
        filter_result: PreFilterResult
    ) -> AnalysisResponse:
        """分析单个帧"""
        # 创建分析请求
        request = AnalysisRequest(
            frame=frame,
            filter_result=filter_result
        )
        
        # 如果服务正在运行，添加到队列
        if self._running:
            success = self._analysis_queue.add_request(request)
            if not success:
                return AnalysisResponse(
                    request=request,
                    analysis_result=None,
                    processing_time_ms=0.0,
                    success=False,
                    error_message="分析队列已满"
                )
        
        # 直接分析（同步模式）
        return await self._process_single_request(request)
    
    async def _analysis_worker(self, worker_name: str):
        """分析工作器"""
        logger.info(f"分析工作器已启动: {worker_name}")
        
        while self._running:
            try:
                # 获取下一个请求
                request = self._analysis_queue.get_next_request()
                if not request:
                    await asyncio.sleep(0.1)
                    continue
                
                # 处理请求
                response = await self._process_single_request(request)
                
                # 处理结果
                await self._handle_analysis_response(response)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"分析工作器 {worker_name} 出错: {e}")
                await asyncio.sleep(1)
        
        logger.info(f"分析工作器已停止: {worker_name}")
    
    async def _batch_processor(self):
        """批量处理器"""
        logger.info("批量处理器已启动")
        
        while self._running:
            try:
                # 获取批量请求
                batch_requests = self._analysis_queue.get_batch_requests(self.batch_size)
                if not batch_requests:
                    await asyncio.sleep(0.5)
                    continue
                
                # 批量分析
                responses = await self._process_batch_requests(batch_requests)
                
                # 处理所有响应
                for response in responses:
                    await self._handle_analysis_response(response)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"批量处理器出错: {e}")
                await asyncio.sleep(1)
        
        logger.info("批量处理器已停止")
    
    async def _cleanup_worker(self):
        """清理工作器"""
        logger.info("清理工作器已启动")
        
        while self._running:
            try:
                # 清理过期请求
                self._analysis_queue.clear_old_requests()
                
                # 等待30秒
                await asyncio.sleep(30)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理工作器出错: {e}")
                await asyncio.sleep(10)
        
        logger.info("清理工作器已停止")
    
    async def _process_single_request(self, request: AnalysisRequest) -> AnalysisResponse:
        """处理单个分析请求"""
        start_time = time.time()
        source_id = request.frame.source_id
        
        try:
            # 准备帧数据
            frame_data = FrameData(
                frame_id=request.frame.frame_id,
                timestamp=request.frame.timestamp,
                image_data=request.frame.frame_data,
                metadata=request.frame.metadata
            )
            
            # 添加预过滤结果到元数据
            if request.filter_result.motion_result:
                frame_data.metadata.update({
                    "motion_score": request.filter_result.motion_result.motion_score,
                    "motion_areas": request.filter_result.motion_result.motion_areas,
                    "has_significant_motion": request.filter_result.motion_result.has_significant_motion
                })
            
            # 执行AI分析
            analysis_result = await self.ai_analyzer.analyze_frame(
                source_id=source_id,
                frame_data=frame_data
            )
            
            processing_time = (time.time() - start_time) * 1000
            
            # 更新统计
            self._update_statistics(source_id, True, processing_time, request.analysis_mode)
            
            return AnalysisResponse(
                request=request,
                analysis_result=analysis_result,
                processing_time_ms=processing_time,
                success=True,
                metadata={
                    "analysis_mode": request.analysis_mode.value,
                    "priority": request.priority
                }
            )
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            error_message = f"AI分析失败: {str(e)}"
            
            logger.error(f"AI分析失败 - 源: {source_id}, 错误: {e}")
            
            # 更新统计
            self._update_statistics(source_id, False, processing_time, request.analysis_mode)
            
            return AnalysisResponse(
                request=request,
                analysis_result=None,
                processing_time_ms=processing_time,
                success=False,
                error_message=error_message
            )
    
    async def _process_batch_requests(self, requests: List[AnalysisRequest]) -> List[AnalysisResponse]:
        """处理批量分析请求"""
        if not requests:
            return []
        
        logger.info(f"开始批量分析，请求数量: {len(requests)}")
        
        # 目前顺序处理，后续可以优化为真正的批量API调用
        responses = []
        for request in requests:
            response = await self._process_single_request(request)
            responses.append(response)
        
        return responses
    
    async def _handle_analysis_response(self, response: AnalysisResponse):
        """处理分析响应"""
        if not response.success or not response.analysis_result:
            return
        
        analysis_result = response.analysis_result
        source_id = response.request.frame.source_id
        
        # 检查是否检测到异常
        if analysis_result.anomalies_detected:
            try:
                # 生成预警事件
                alert_events = await self._generate_alert_events(
                    response.request.frame,
                    analysis_result
                )
                
                response.generated_alerts = alert_events
                
                # 保存证据文件
                for alert_event in alert_events:
                    await self._save_evidence_files(
                        response.request.frame,
                        alert_event
                    )
                
                # 更新统计
                with self._lock:
                    if source_id not in self._statistics:
                        self._statistics[source_id] = AIIntegrationStatistics()
                    
                    stats = self._statistics[source_id]
                    stats.alerts_generated += len(alert_events)
                    stats.anomalies_detected += len(analysis_result.detected_behaviors)
                    
                    self._global_statistics.alerts_generated += len(alert_events)
                    self._global_statistics.anomalies_detected += len(analysis_result.detected_behaviors)
                
            except Exception as e:
                logger.error(f"处理预警生成失败 - 源: {source_id}, 错误: {e}")
    
    async def _generate_alert_events(
        self, 
        frame: VideoFrame, 
        analysis_result: AnalysisResult
    ) -> List[AlertEvent]:
        """生成预警事件"""
        alert_events = []
        
        for behavior in analysis_result.detected_behaviors:
            alert_event = AlertEvent(
                camera_id=frame.source_id,
                behavior_type=behavior.behavior_type,
                confidence=behavior.confidence,
                description=behavior.description,
                frame_timestamp=frame.timestamp,
                detection_area=behavior.bounding_box,
                metadata={
                    "frame_id": frame.frame_id,
                    "analysis_timestamp": analysis_result.analysis_timestamp,
                    "ai_model": analysis_result.model_info.get("model_name", "unknown"),
                    "processing_time_ms": analysis_result.processing_time_ms
                }
            )
            
            # 使用预警服务创建预警
            created_alert = await self.alert_service.create_alert(alert_event)
            if created_alert:
                alert_events.append(created_alert)
        
        return alert_events
    
    async def _save_evidence_files(self, frame: VideoFrame, alert_event: AlertEvent):
        """保存证据文件"""
        try:
            # 保存帧图片
            image_filename = f"evidence_{alert_event.id}_{frame.frame_id}.jpg"
            image_url = await self.storage_service.upload_evidence_file(
                file_data=frame.frame_data,
                filename=image_filename,
                file_type="image"
            )
            
            if image_url:
                # 更新预警事件的证据文件信息
                await self.alert_service.add_evidence_file(
                    alert_event.id,
                    {
                        "file_url": image_url,
                        "file_type": "image",
                        "filename": image_filename,
                        "description": "异常行为检测证据图片"
                    }
                )
        
        except Exception as e:
            logger.error(f"保存证据文件失败 - 预警ID: {alert_event.id}, 错误: {e}")
    
    def _update_statistics(
        self, 
        source_id: str, 
        success: bool, 
        processing_time_ms: float,
        analysis_mode: AnalysisMode
    ):
        """更新统计信息"""
        with self._lock:
            if source_id not in self._statistics:
                self._statistics[source_id] = AIIntegrationStatistics()
            
            stats = self._statistics[source_id]
            global_stats = self._global_statistics
            
            # 更新请求统计
            stats.total_requests += 1
            global_stats.total_requests += 1
            
            if success:
                stats.successful_analyses += 1
                global_stats.successful_analyses += 1
            else:
                stats.failed_analyses += 1
                global_stats.failed_analyses += 1
            
            # 更新分析模式统计
            if analysis_mode == AnalysisMode.SINGLE_FRAME:
                stats.single_frame_analyses += 1
                global_stats.single_frame_analyses += 1
            elif analysis_mode == AnalysisMode.BATCH_ANALYSIS:
                stats.batch_analyses += 1
                global_stats.batch_analyses += 1
            elif analysis_mode == AnalysisMode.PRIORITY_ANALYSIS:
                stats.priority_analyses += 1
                global_stats.priority_analyses += 1
            
            # 更新处理时间
            stats.total_processing_time_ms += processing_time_ms
            global_stats.total_processing_time_ms += processing_time_ms
            
            if stats.total_requests > 0:
                stats.avg_processing_time_ms = stats.total_processing_time_ms / stats.total_requests
            
            if global_stats.total_requests > 0:
                global_stats.avg_processing_time_ms = global_stats.total_processing_time_ms / global_stats.total_requests
            
            # 计算性能分数
            stats.performance_score = self._calculate_performance_score(stats)
            global_stats.performance_score = self._calculate_performance_score(global_stats)
    
    def _calculate_performance_score(self, stats: AIIntegrationStatistics) -> float:
        """计算性能分数"""
        if stats.total_requests == 0:
            return 0.0
        
        # 成功率分数 (40%)
        success_rate = stats.successful_analyses / stats.total_requests
        success_score = success_rate * 40
        
        # 处理速度分数 (30%)
        speed_score = max(0, 100 - stats.avg_processing_time_ms / 100) * 0.3
        
        # 异常检测效率分数 (30%)
        detection_rate = min(stats.anomalies_detected / max(stats.total_requests, 1), 0.1) * 10
        detection_score = detection_rate * 30
        
        return min(success_score + speed_score + detection_score, 100.0)
    
    def get_statistics(self, source_id: Optional[str] = None) -> Dict[str, AIIntegrationStatistics]:
        """获取统计信息"""
        with self._lock:
            if source_id:
                return {source_id: self._statistics.get(source_id, AIIntegrationStatistics())}
            else:
                return dict(self._statistics)
    
    def get_global_statistics(self) -> AIIntegrationStatistics:
        """获取全局统计信息"""
        with self._lock:
            return self._global_statistics
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        queue_status = self._analysis_queue.get_queue_status()
        queue_status.update({
            "running": self._running,
            "workers": len(self._workers),
            "max_concurrent": self.max_concurrent_analyses
        })
        return queue_status 