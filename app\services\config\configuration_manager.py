"""
配置管理器 - 统一配置管理核心
提供配置缓存、热更新、事件通知等功能
支持Redis缓存和配置版本控制
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any, Callable, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import hashlib

from .config_service import ConfigService
from ...core.cache import RedisCache
from ...core.exceptions import ConfigurationError

logger = logging.getLogger(__name__)


class ConfigChangeType(Enum):
    """配置变更类型"""
    CAMERA_CONFIG = "camera_config"
    ANALYSIS_RULE = "analysis_rule"
    BEHAVIOR_MAPPING = "behavior_mapping"
    PROMPT_TEMPLATE = "prompt_template"
    SYSTEM_CONFIG = "system_config"


@dataclass
class ConfigVersion:
    """配置版本信息"""
    config_key: str
    version: str
    timestamp: datetime
    checksum: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    change_type: ConfigChangeType
    resource_id: str
    old_version: Optional[str] = None
    new_version: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ConfigurationManager:
    """配置管理器"""
    
    def __init__(
        self,
        config_service: ConfigService,
        redis_cache: RedisCache,
        cache_ttl: int = 1800,  # 30分钟缓存
        hot_reload_enabled: bool = True,
        check_interval: int = 60  # 1分钟检查一次配置变更
    ):
        self.config_service = config_service
        self.redis_cache = redis_cache
        self.cache_ttl = cache_ttl
        self.hot_reload_enabled = hot_reload_enabled
        self.check_interval = check_interval
        
        # 配置缓存
        self._local_cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        self._config_versions: Dict[str, ConfigVersion] = {}
        
        # 变更监听器
        self._change_listeners: Dict[ConfigChangeType, List[Callable]] = {
            change_type: [] for change_type in ConfigChangeType
        }
        
        # 配置依赖关系
        self._config_dependencies: Dict[str, Set[str]] = {}
        
        # 热更新任务
        self._hot_reload_task: Optional[asyncio.Task] = None
        self._is_running = False
        
        # 统计信息
        self._stats = {
            "cache_hits": 0,
            "cache_misses": 0,
            "config_refreshes": 0,
            "hot_reload_events": 0,
            "last_check_time": None
        }
        
        logger.info("配置管理器初始化完成")
    
    async def start(self):
        """启动配置管理器"""
        if self._is_running:
            logger.warning("配置管理器已在运行")
            return
        
        try:
            self._is_running = True
            
            # 预热缓存
            await self._warm_up_cache()
            
            # 启动热更新任务
            if self.hot_reload_enabled:
                self._hot_reload_task = asyncio.create_task(self._hot_reload_loop())
            
            logger.info("配置管理器已启动")
            
        except Exception as e:
            logger.error(f"启动配置管理器失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止配置管理器"""
        if not self._is_running:
            return
        
        try:
            self._is_running = False
            
            # 停止热更新任务
            if self._hot_reload_task:
                self._hot_reload_task.cancel()
                try:
                    await self._hot_reload_task
                except asyncio.CancelledError:
                    pass
            
            # 清理缓存
            self._local_cache.clear()
            self._cache_timestamps.clear()
            
            logger.info("配置管理器已停止")
            
        except Exception as e:
            logger.error(f"停止配置管理器失败: {e}")
    
    async def get_camera_config(self, camera_id: int, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """获取摄像头配置"""
        config_key = f"camera_config:{camera_id}"
        
        try:
            # 检查缓存
            if use_cache:
                cached_config = await self._get_from_cache(config_key)
                if cached_config:
                    self._stats["cache_hits"] += 1
                    return cached_config
            
            self._stats["cache_misses"] += 1
            
            # 从配置服务获取
            config = await self.config_service.get_camera_analysis_config(camera_id)
            if not config:
                return None
            
            # 计算配置版本
            version = self._calculate_config_version(config)
            
            # 缓存配置
            await self._set_to_cache(config_key, config, version)
            
            logger.debug(f"摄像头 {camera_id} 配置已获取并缓存")
            return config
            
        except Exception as e:
            logger.error(f"获取摄像头 {camera_id} 配置失败: {e}")
            raise ConfigurationError(f"获取摄像头配置失败: {e}")
    
    async def get_ai_prompt(self, camera_id: int, use_cache: bool = True) -> Optional[str]:
        """获取AI分析提示词"""
        config_key = f"ai_prompt:{camera_id}"
        
        try:
            # 检查缓存
            if use_cache:
                cached_prompt = await self._get_from_cache(config_key)
                if cached_prompt:
                    self._stats["cache_hits"] += 1
                    return cached_prompt
            
            self._stats["cache_misses"] += 1
            
            # 从配置服务获取
            prompt = await self.config_service.get_ai_prompt_for_camera(camera_id)
            if not prompt:
                return None
            
            # 计算版本
            version = self._calculate_config_version(prompt)
            
            # 缓存提示词
            await self._set_to_cache(config_key, prompt, version)
            
            return prompt
            
        except Exception as e:
            logger.error(f"获取摄像头 {camera_id} AI提示词失败: {e}")
            raise ConfigurationError(f"获取AI提示词失败: {e}")
    
    async def refresh_camera_config(self, camera_id: int) -> bool:
        """刷新摄像头配置"""
        try:
            config_key = f"camera_config:{camera_id}"
            
            # 获取新配置
            new_config = await self.config_service.get_camera_analysis_config(camera_id)
            if not new_config:
                return False
            
            # 检查配置是否变更
            old_version = self._config_versions.get(config_key)
            new_version_str = self._calculate_config_version(new_config)
            
            if old_version and old_version.version == new_version_str:
                logger.debug(f"摄像头 {camera_id} 配置未变更")
                return True
            
            # 更新缓存
            await self._set_to_cache(config_key, new_config, new_version_str)
            
            # 发送配置变更事件
            change_event = ConfigChangeEvent(
                change_type=ConfigChangeType.CAMERA_CONFIG,
                resource_id=str(camera_id),
                old_version=old_version.version if old_version else None,
                new_version=new_version_str,
                metadata={"config_key": config_key}
            )
            
            await self._notify_config_change(change_event)
            
            self._stats["config_refreshes"] += 1
            logger.info(f"摄像头 {camera_id} 配置已刷新")
            
            return True
            
        except Exception as e:
            logger.error(f"刷新摄像头 {camera_id} 配置失败: {e}")
            return False
    
    async def refresh_all_camera_configs(self) -> Dict[str, bool]:
        """刷新所有摄像头配置"""
        try:
            results = {}
            
            # 获取所有摄像头ID（从缓存键中提取）
            camera_ids = set()
            for config_key in self._config_versions.keys():
                if config_key.startswith("camera_config:"):
                    camera_id = int(config_key.split(":")[1])
                    camera_ids.add(camera_id)
            
            # 并发刷新配置
            refresh_tasks = [
                self.refresh_camera_config(camera_id) 
                for camera_id in camera_ids
            ]
            
            if refresh_tasks:
                refresh_results = await asyncio.gather(*refresh_tasks, return_exceptions=True)
                
                for camera_id, result in zip(camera_ids, refresh_results):
                    if isinstance(result, Exception):
                        results[str(camera_id)] = False
                        logger.error(f"刷新摄像头 {camera_id} 配置失败: {result}")
                    else:
                        results[str(camera_id)] = result
            
            logger.info(f"批量刷新完成: 成功 {sum(results.values())}/{len(results)}")
            return results
            
        except Exception as e:
            logger.error(f"批量刷新配置失败: {e}")
            return {}
    
    def add_config_change_listener(
        self, 
        change_type: ConfigChangeType, 
        callback: Callable[[ConfigChangeEvent], None]
    ):
        """添加配置变更监听器"""
        if change_type not in self._change_listeners:
            self._change_listeners[change_type] = []
        
        self._change_listeners[change_type].append(callback)
        logger.debug(f"已添加 {change_type.value} 配置变更监听器")
    
    def remove_config_change_listener(
        self, 
        change_type: ConfigChangeType, 
        callback: Callable[[ConfigChangeEvent], None]
    ):
        """移除配置变更监听器"""
        if change_type in self._change_listeners:
            try:
                self._change_listeners[change_type].remove(callback)
                logger.debug(f"已移除 {change_type.value} 配置变更监听器")
            except ValueError:
                pass
    
    async def _get_from_cache(self, config_key: str) -> Optional[Any]:
        """从缓存获取配置"""
        try:
            # 先检查本地缓存
            if config_key in self._local_cache:
                cache_time = self._cache_timestamps.get(config_key)
                if cache_time and datetime.utcnow() - cache_time < timedelta(seconds=300):  # 5分钟本地缓存
                    return self._local_cache[config_key]
            
            # 从Redis获取
            cached_data = await self.redis_cache.get(config_key)
            if cached_data:
                try:
                    config_data = json.loads(cached_data)
                    
                    # 更新本地缓存
                    self._local_cache[config_key] = config_data["data"]
                    self._cache_timestamps[config_key] = datetime.utcnow()
                    
                    # 更新版本信息
                    if "version" in config_data:
                        self._config_versions[config_key] = ConfigVersion(
                            config_key=config_key,
                            version=config_data["version"],
                            timestamp=datetime.fromisoformat(config_data["timestamp"]),
                            checksum=config_data.get("checksum", ""),
                            metadata=config_data.get("metadata", {})
                        )
                    
                    return config_data["data"]
                except (json.JSONDecodeError, KeyError) as e:
                    logger.warning(f"解析缓存数据失败: {e}")
                    await self.redis_cache.delete(config_key)
            
            return None
            
        except Exception as e:
            logger.error(f"从缓存获取配置失败: {e}")
            return None
    
    async def _set_to_cache(self, config_key: str, data: Any, version: str):
        """设置配置到缓存"""
        try:
            timestamp = datetime.utcnow()
            checksum = self._calculate_checksum(data)
            
            cache_data = {
                "data": data,
                "version": version,
                "timestamp": timestamp.isoformat(),
                "checksum": checksum,
                "metadata": {}
            }
            
            # 存储到Redis
            await self.redis_cache.set(
                config_key, 
                json.dumps(cache_data, ensure_ascii=False, default=str),
                ttl=self.cache_ttl
            )
            
            # 更新本地缓存
            self._local_cache[config_key] = data
            self._cache_timestamps[config_key] = timestamp
            
            # 更新版本信息
            self._config_versions[config_key] = ConfigVersion(
                config_key=config_key,
                version=version,
                timestamp=timestamp,
                checksum=checksum
            )
            
        except Exception as e:
            logger.error(f"设置配置到缓存失败: {e}")
    
    def _calculate_config_version(self, config_data: Any) -> str:
        """计算配置版本号"""
        try:
            config_str = json.dumps(config_data, sort_keys=True, ensure_ascii=False, default=str)
            checksum = hashlib.md5(config_str.encode()).hexdigest()
            timestamp = int(time.time())
            return f"v{timestamp}_{checksum[:8]}"
        except Exception as e:
            logger.error(f"计算配置版本失败: {e}")
            return f"v{int(time.time())}_unknown"
    
    def _calculate_checksum(self, data: Any) -> str:
        """计算数据校验和"""
        try:
            data_str = json.dumps(data, sort_keys=True, ensure_ascii=False, default=str)
            return hashlib.sha256(data_str.encode()).hexdigest()
        except Exception as e:
            logger.error(f"计算校验和失败: {e}")
            return ""
    
    async def _notify_config_change(self, change_event: ConfigChangeEvent):
        """通知配置变更"""
        try:
            listeners = self._change_listeners.get(change_event.change_type, [])
            
            if listeners:
                # 异步通知所有监听器
                notify_tasks = []
                for listener in listeners:
                    if asyncio.iscoroutinefunction(listener):
                        notify_tasks.append(listener(change_event))
                    else:
                        # 同步函数在线程池中执行
                        loop = asyncio.get_event_loop()
                        notify_tasks.append(loop.run_in_executor(None, listener, change_event))
                
                if notify_tasks:
                    await asyncio.gather(*notify_tasks, return_exceptions=True)
                
                logger.debug(f"已通知 {len(listeners)} 个 {change_event.change_type.value} 监听器")
            
        except Exception as e:
            logger.error(f"通知配置变更失败: {e}")
    
    async def _warm_up_cache(self):
        """预热缓存"""
        try:
            logger.info("开始预热配置缓存...")
            
            # 这里可以预加载一些重要的配置
            # 例如：活跃摄像头的配置
            # 由于我们没有获取所有摄像头的方法，这里暂时跳过
            
            logger.info("配置缓存预热完成")
            
        except Exception as e:
            logger.error(f"预热缓存失败: {e}")
    
    async def _hot_reload_loop(self):
        """热更新循环"""
        logger.info("配置热更新任务已启动")
        
        while self._is_running:
            try:
                await asyncio.sleep(self.check_interval)
                
                if not self._is_running:
                    break
                
                # 检查配置变更
                await self._check_config_changes()
                
                self._stats["last_check_time"] = datetime.utcnow()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"热更新检查失败: {e}")
                await asyncio.sleep(30)  # 错误后延迟30秒再检查
    
    async def _check_config_changes(self):
        """检查配置变更"""
        try:
            # 这里可以实现配置变更检测逻辑
            # 例如：检查数据库表的更新时间戳
            # 由于缺少具体的变更检测机制，这里暂时跳过实现
            
            logger.debug("配置变更检查完成")
            
        except Exception as e:
            logger.error(f"检查配置变更失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "cache_hits": self._stats["cache_hits"],
            "cache_misses": self._stats["cache_misses"],
            "cache_hit_rate": (
                self._stats["cache_hits"] / 
                (self._stats["cache_hits"] + self._stats["cache_misses"])
                if (self._stats["cache_hits"] + self._stats["cache_misses"]) > 0 else 0
            ),
            "config_refreshes": self._stats["config_refreshes"],
            "hot_reload_events": self._stats["hot_reload_events"],
            "last_check_time": self._stats["last_check_time"],
            "cached_configs": len(self._local_cache),
            "config_versions": len(self._config_versions),
            "change_listeners": {
                change_type.value: len(listeners) 
                for change_type, listeners in self._change_listeners.items()
            },
            "is_running": self._is_running,
            "hot_reload_enabled": self.hot_reload_enabled
        }
    
    async def clear_cache(self, config_pattern: Optional[str] = None):
        """清理缓存"""
        try:
            if config_pattern:
                # 清理匹配模式的缓存
                keys_to_remove = [
                    key for key in self._local_cache.keys() 
                    if config_pattern in key
                ]
                
                for key in keys_to_remove:
                    self._local_cache.pop(key, None)
                    self._cache_timestamps.pop(key, None)
                    self._config_versions.pop(key, None)
                    await self.redis_cache.delete(key)
                
                logger.info(f"已清理 {len(keys_to_remove)} 个匹配 '{config_pattern}' 的缓存")
            else:
                # 清理所有缓存
                self._local_cache.clear()
                self._cache_timestamps.clear()
                self._config_versions.clear()
                
                # 清理Redis缓存（这里需要根据实际情况实现）
                logger.info("已清理所有配置缓存")
                
        except Exception as e:
            logger.error(f"清理缓存失败: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            health_status = {
                "status": "healthy",
                "is_running": self._is_running,
                "hot_reload_enabled": self.hot_reload_enabled,
                "cache_status": "ok",
                "issues": []
            }
            
            # 检查Redis连接
            try:
                await self.redis_cache.ping()
            except Exception as e:
                health_status["cache_status"] = "error"
                health_status["issues"].append(f"Redis连接失败: {e}")
            
            # 检查热更新任务
            if self.hot_reload_enabled and (not self._hot_reload_task or self._hot_reload_task.done()):
                health_status["issues"].append("热更新任务未运行")
            
            # 检查缓存命中率
            stats = self.get_statistics()
            if stats["cache_hit_rate"] < 0.5:  # 命中率低于50%
                health_status["issues"].append("缓存命中率过低")
            
            if health_status["issues"]:
                health_status["status"] = "warning" if len(health_status["issues"]) < 3 else "unhealthy"
            
            return health_status
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "is_running": self._is_running
            } 