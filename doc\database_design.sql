-- =====================================================
-- 智能视频监控预警系统 (KTV版) - 数据库设计
-- 版本: 1.0
-- 创建时间: 2024-12-19
-- 描述: 基于MySQL 8.0+的完整数据库结构设计
-- =====================================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 2. 门店设备管理域 (Store & Device Domain)
-- =====================================================

-- 2.1 门店表
DROP TABLE IF EXISTS `stores`;
CREATE TABLE `stores` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '门店ID',
  `name` varchar(100) NOT NULL COMMENT '门店名称',
  `code` varchar(50) NOT NULL COMMENT '门店编号',
  `type` enum('flagship','standard','mini') NOT NULL DEFAULT 'standard' COMMENT '门店类型',
  `district` varchar(50) NOT NULL COMMENT '所在区域',
  `address` varchar(255) NOT NULL COMMENT '详细地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `business_hours` varchar(50) DEFAULT NULL COMMENT '营业时间',
  `manager_name` varchar(50) DEFAULT NULL COMMENT '负责人姓名',
  `manager_phone` varchar(20) DEFAULT NULL COMMENT '负责人电话',
  `status` enum('normal','warning','offline','disabled') NOT NULL DEFAULT 'normal' COMMENT '门店状态',
  `camera_count` int unsigned NOT NULL DEFAULT '0' COMMENT '摄像头数量',
  `online_camera_count` int unsigned NOT NULL DEFAULT '0' COMMENT '在线摄像头数量',
  `coordinates` point DEFAULT NULL COMMENT '地理坐标',
  `metadata` json DEFAULT NULL COMMENT '扩展元数据',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_name` (`name`),
  KEY `idx_district` (`district`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='门店表';

-- 2.2 摄像头设备表   MARK:摄像头 —— 配置多个AI算法 —— 每个算法检测多种异常行为 —— 配置专用提示词
DROP TABLE IF EXISTS `cameras`;
CREATE TABLE `cameras` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '摄像头ID',
  `store_id` bigint unsigned NOT NULL COMMENT '所属门店ID',
  `name` varchar(100) NOT NULL COMMENT '摄像头名称',
  `code` varchar(50) NOT NULL COMMENT '设备编号',
  `location` varchar(100) NOT NULL COMMENT '安装位置描述',
  `specific_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '具体位置',
  `ip_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `mac_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'MAC地址',
  `rtsp_url` varchar(500) NOT NULL COMMENT 'RTSP流地址',
  `rtsp_backup_url` varchar(500) DEFAULT NULL COMMENT '备用RTSP流地址',
  `brand` varchar(50) DEFAULT NULL COMMENT '设备品牌',
  `model` varchar(50) DEFAULT NULL COMMENT '设备型号',
  `resolution` varchar(20) DEFAULT NULL COMMENT '分辨率',
  `fps` int unsigned DEFAULT '25' COMMENT '帧率',
  `analysis_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用AI分析',
  `analysis_fps` decimal(3,1) DEFAULT '1.0' COMMENT 'AI分析帧率',
  `analysis_scenarios` json DEFAULT NULL COMMENT '适用分析场景',
  `status` enum('online','offline','error','maintenance') NOT NULL DEFAULT 'offline' COMMENT '设备状态',
  `last_heartbeat_at` timestamp NULL DEFAULT NULL COMMENT '最后心跳时间',
  `connection_info` json DEFAULT NULL COMMENT '连接信息',
  `position_x` decimal(10,6) DEFAULT NULL COMMENT 'X坐标（相对位置）',
  `position_y` decimal(10,6) DEFAULT NULL COMMENT 'Y坐标（相对位置）',
  `view_angle` int DEFAULT NULL COMMENT '视角角度',
  `metadata` json DEFAULT NULL COMMENT '扩展元数据',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_name` (`name`),
  KEY `idx_location` (`location`),
  KEY `idx_status` (`status`),
  KEY `idx_analysis_enabled` (`analysis_enabled`),
  KEY `idx_last_heartbeat` (`last_heartbeat_at`),
  CONSTRAINT `fk_cameras_store` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='摄像头设备表';

-- =====================================================
-- 3. AI分析与预警管理域 (AI Analysis & Alert Domain)
-- =====================================================

-- 3.1 异常行为定义表
DROP TABLE IF EXISTS `anomaly_behaviors`;
CREATE TABLE `anomaly_behaviors` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '行为ID',
  `name` varchar(100) NOT NULL COMMENT '行为名称',
  `code` varchar(50) NOT NULL COMMENT '行为代码',
  `category` enum('violence','accident','security','fire','crowd','other') NOT NULL COMMENT '行为分类',
  `description` text NOT NULL COMMENT '行为描述',
  `ai_keywords` json NOT NULL COMMENT 'AI识别关键词',
  `default_severity` enum('critical','high','medium','low') NOT NULL DEFAULT 'medium' COMMENT '默认严重等级',
  `default_confidence_threshold` decimal(3,2) NOT NULL DEFAULT '0.70' COMMENT '默认置信度阈值',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `sort_order` int unsigned NOT NULL DEFAULT '0' COMMENT '排序权重',
  `metadata` json DEFAULT NULL COMMENT '扩展元数据',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='异常行为定义表';

-- 3.2 AI算法分析规则表
DROP TABLE IF EXISTS `analysis_rules`;
CREATE TABLE `analysis_rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `name` varchar(100) NOT NULL COMMENT '规则名称',
  `store_id` bigint unsigned DEFAULT NULL COMMENT '适用门店ID（NULL表示全局）',
  `camera_id` bigint unsigned NOT NULL COMMENT '摄像头ID',
  `rule_type` enum('simple','sequence','complex') NOT NULL DEFAULT 'simple' COMMENT '规则类型',
  `trigger_conditions` json NOT NULL COMMENT '触发条件配置',
  `confidence_threshold` decimal(3,2) NOT NULL DEFAULT '0.70' COMMENT '置信度阈值',
  `consecutive_frames` int unsigned NOT NULL DEFAULT '3' COMMENT '连续帧数要求',
  `time_window_seconds` int unsigned DEFAULT '30' COMMENT '时间窗口（秒）',
  `cooldown_seconds` int unsigned DEFAULT '60' COMMENT '冷却时间（秒）',
  `severity_override` enum('critical','high','medium','low') DEFAULT NULL COMMENT '严重等级覆盖',
  `apply_scope` enum('all_cameras','specific_cameras','store_cameras') NOT NULL DEFAULT 'all_cameras' COMMENT '应用范围',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `priority` int unsigned NOT NULL DEFAULT '100' COMMENT '规则优先级',
  `metadata` json DEFAULT NULL COMMENT '扩展配置',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI分析规则表';

-- 3.2.1 规则异常行为关联表
DROP TABLE IF EXISTS `rule_behavior_mappings`;
CREATE TABLE `rule_behavior_mappings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `rule_id` bigint unsigned NOT NULL COMMENT '规则ID',
  `behavior_id` bigint unsigned NOT NULL COMMENT '异常行为ID',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用该行为检测',
  `confidence_threshold_override` decimal(3,2) DEFAULT NULL COMMENT '置信度阈值覆盖',
  `severity_override` enum('critical','high','medium','low') DEFAULT NULL COMMENT '严重等级覆盖',
  `weight` decimal(3,2) NOT NULL DEFAULT '1.00' COMMENT '该行为在规则中的权重',
  `custom_params` json DEFAULT NULL COMMENT '该行为的自定义参数',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='规则异常行为关联表';

-- 3.3 预警事件表   AI检测 —— 算法配置 —— 异常行为 —— 生成预警 —— 关联证据文件
DROP TABLE IF EXISTS `alerts`;
CREATE TABLE `alerts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '预警ID',
  `uuid` varchar(36) NOT NULL COMMENT '预警唯一标识',
  `store_id` bigint unsigned NOT NULL COMMENT '门店ID',
  `camera_id` bigint unsigned NOT NULL COMMENT '摄像头ID',
  `behavior_id` bigint unsigned NOT NULL COMMENT '异常行为ID',
  `rule_id` bigint unsigned NOT NULL COMMENT '触发规则ID',
  `title` varchar(200) NOT NULL COMMENT '预警标题',
  `description` text DEFAULT NULL COMMENT '预警描述',
  `severity` enum('critical','high','medium','low') NOT NULL COMMENT '严重等级',
  `confidence_score` decimal(5,4) NOT NULL COMMENT '置信度分数',
  `status` enum('pending','processing','resolved','false_positive','ignored') NOT NULL DEFAULT 'pending' COMMENT '处理状态',
  `trigger_time` timestamp NOT NULL COMMENT '触发时间',
  `first_detected_at` timestamp NOT NULL COMMENT '首次检测时间',
  `last_detected_at` timestamp NOT NULL COMMENT '最后检测时间',
  `detection_count` int unsigned NOT NULL DEFAULT '1' COMMENT '检测次数',
  `ai_analysis_result` json DEFAULT NULL COMMENT 'AI分析原始结果',
  `trigger_frames_info` json DEFAULT NULL COMMENT '触发帧信息',
  `location_info` json DEFAULT NULL COMMENT '位置信息',
  `assigned_to` bigint unsigned DEFAULT NULL COMMENT '分配处理人',
  `processed_by` bigint unsigned DEFAULT NULL COMMENT '实际处理人',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `resolution_notes` text DEFAULT NULL COMMENT '处理备注',
  `evidence_count` int unsigned NOT NULL DEFAULT '0' COMMENT '证据文件数量',
  `notification_sent` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已发送通知',
  `is_important` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否重要事件',
  `tags` json DEFAULT NULL COMMENT '事件标签',
  `metadata` json DEFAULT NULL COMMENT '扩展元数据',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预警事件表'

-- 3.4 证据文件表
DROP TABLE IF EXISTS `evidence_files`;
CREATE TABLE `evidence_files` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '证据ID',
  `alert_id` bigint unsigned NOT NULL COMMENT '关联预警ID',
  `file_storage_id` bigint unsigned NOT NULL COMMENT '关联文件存储ID',
  `evidence_type` enum('auto_capture','manual_upload','system_generated') NOT NULL DEFAULT 'auto_capture' COMMENT '证据类型',
  `capture_time` timestamp NOT NULL COMMENT '捕获时间',
  `frame_index` int unsigned DEFAULT NULL COMMENT '帧索引（图片）',
  `detection_confidence` decimal(5,4) DEFAULT NULL COMMENT '检测置信度',
  `is_key_evidence` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否关键证据',
  `evidence_category` enum('before_event','during_event','after_event') NOT NULL DEFAULT 'during_event' COMMENT '证据时间分类',
  `analysis_result` json DEFAULT NULL COMMENT '分析结果',
  `legal_status` enum('pending','verified','rejected','sealed') NOT NULL DEFAULT 'pending' COMMENT '法律状态',
  `chain_of_custody` json DEFAULT NULL COMMENT '监管链记录',
  `retention_reason` varchar(200) DEFAULT NULL COMMENT '保留原因',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='证据文件表';

-- =====================================================
-- 5. 系统配置管理域 (System Configuration Domain)
-- =====================================================

-- 5.1 提示词模板管理表   算法行为专用模板 > 行为默认模板 > 全局默认模板
DROP TABLE IF EXISTS `prompt_templates`;
CREATE TABLE `prompt_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `code` varchar(50) NOT NULL COMMENT '模板代码',
  `category` varchar(50) NOT NULL COMMENT '模板分类',
  `behavior_id` bigint unsigned DEFAULT NULL COMMENT '关联异常行为ID',
  `scenario` varchar(100) DEFAULT NULL COMMENT '适用场景',
  `template_content` text NOT NULL COMMENT '模板内容',
  `variables` json DEFAULT NULL COMMENT '模板变量定义',
  `model_type` varchar(50) DEFAULT NULL COMMENT '适用AI模型类型',
  `model_version` varchar(50) DEFAULT NULL COMMENT '适用模型版本',
  `temperature` decimal(3,2) DEFAULT '0.70' COMMENT '温度参数',
  `max_tokens` int unsigned DEFAULT '1000' COMMENT '最大令牌数',
  `system_prompt` text DEFAULT NULL COMMENT '系统提示词',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认模板',
  `version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '模板版本',
  `performance_score` decimal(5,4) DEFAULT NULL COMMENT '性能评分',
  `usage_count` bigint unsigned NOT NULL DEFAULT '0' COMMENT '使用次数',
  `success_rate` decimal(5,4) DEFAULT NULL COMMENT '成功率',
  `avg_response_time_ms` int unsigned DEFAULT NULL COMMENT '平均响应时间（毫秒）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词模板管理表';

-- 5.2 API密钥管理表
DROP TABLE IF EXISTS `api_keys`;
CREATE TABLE `api_keys` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '密钥ID',
  `name` varchar(100) NOT NULL COMMENT '密钥名称',
  `service_type` varchar(50) NOT NULL COMMENT '服务类型',
  `provider` varchar(50) NOT NULL COMMENT '服务提供商',
  `key_id` varchar(100) DEFAULT NULL COMMENT '密钥标识',
  `encrypted_key` text NOT NULL COMMENT '加密后的密钥',
  `encryption_method` varchar(50) NOT NULL DEFAULT 'AES-256' COMMENT '加密方法',
  `config` json DEFAULT NULL COMMENT '额外配置信息',
  `quota_limit` bigint unsigned DEFAULT NULL COMMENT '配额限制',
  `quota_used` bigint unsigned NOT NULL DEFAULT '0' COMMENT '已使用配额',
  `quota_reset_date` date DEFAULT NULL COMMENT '配额重置日期',
  `rate_limit_per_minute` int unsigned DEFAULT NULL COMMENT '每分钟限制',
  `rate_limit_per_hour` int unsigned DEFAULT NULL COMMENT '每小时限制',
  `rate_limit_per_day` int unsigned DEFAULT NULL COMMENT '每天限制',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API密钥管理表';

-- 5.5 第三方服务配置表
DROP TABLE IF EXISTS `third_party_services`;
CREATE TABLE `third_party_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '服务ID',
  `name` varchar(100) NOT NULL COMMENT '服务名称',
  `code` varchar(50) NOT NULL COMMENT '服务代码',
  `service_type` enum('sms','email','ai_model','storage','webhook','payment','other') NOT NULL COMMENT '服务类型',
  `provider` varchar(50) NOT NULL COMMENT '服务提供商',
  `endpoint_url` varchar(500) DEFAULT NULL COMMENT '服务端点URL',
  `auth_type` enum('none','api_key','oauth2','basic_auth','custom') NOT NULL DEFAULT 'api_key' COMMENT '认证类型',
  `auth_config` json DEFAULT NULL COMMENT '认证配置',
  `request_config` json DEFAULT NULL COMMENT '请求配置',
  `response_config` json DEFAULT NULL COMMENT '响应配置',
  `retry_config` json DEFAULT NULL COMMENT '重试配置',
  `timeout_seconds` int unsigned NOT NULL DEFAULT '30' COMMENT '超时时间（秒）',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `health_check_url` varchar(500) DEFAULT NULL COMMENT '健康检查URL',
  `health_check_interval_minutes` int unsigned DEFAULT '5' COMMENT '健康检查间隔（分钟）',
  `status` enum('active','inactive','error','maintenance') NOT NULL DEFAULT 'active' COMMENT '服务状态',
  `last_health_check_at` timestamp NULL DEFAULT NULL COMMENT '最后健康检查时间',
  `last_error_message` text DEFAULT NULL COMMENT '最后错误信息',
  `success_count` bigint unsigned NOT NULL DEFAULT '0' COMMENT '成功次数',
  `failure_count` bigint unsigned NOT NULL DEFAULT '0' COMMENT '失败次数',
  `avg_response_time_ms` int unsigned DEFAULT NULL COMMENT '平均响应时间（毫秒）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='第三方服务配置表';

-- =====================================================
-- 8. 文件管理域 (File Management Domain)
-- =====================================================

-- 8.1 文件存储表
DROP TABLE IF EXISTS `file_storage`;
CREATE TABLE `file_storage` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `uuid` varchar(36) NOT NULL COMMENT '文件唯一标识',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_type` enum('image','video','audio','document','archive','other') NOT NULL COMMENT '文件类型',
  `mime_type` varchar(100) NOT NULL COMMENT 'MIME类型',
  `file_size` bigint unsigned NOT NULL COMMENT '文件大小（字节）',
  `file_hash` varchar(64) NOT NULL COMMENT '文件哈希值',
  `checksum` varchar(64) DEFAULT NULL COMMENT '校验和',
  `storage_provider` varchar(50) NOT NULL DEFAULT 'minio' COMMENT '存储提供商',
  `storage_bucket` varchar(100) NOT NULL COMMENT '存储桶',
  `storage_key` varchar(500) NOT NULL COMMENT '存储键',
  `storage_region` varchar(50) DEFAULT NULL COMMENT '存储区域',
  `access_url` varchar(1000) DEFAULT NULL COMMENT '访问URL',
  `thumbnail_path` varchar(500) DEFAULT NULL COMMENT '缩略图路径',
  `preview_path` varchar(500) DEFAULT NULL COMMENT '预览文件路径',
  `metadata` json DEFAULT NULL COMMENT '文件元数据',
  `exif_data` json DEFAULT NULL COMMENT 'EXIF数据（图片）',
  `duration_seconds` decimal(8,3) DEFAULT NULL COMMENT '时长（视频/音频）',
  `resolution` varchar(20) DEFAULT NULL COMMENT '分辨率',
  `bitrate` int unsigned DEFAULT NULL COMMENT '比特率',
  `encoding` varchar(50) DEFAULT NULL COMMENT '编码格式',
  `is_encrypted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加密',
  `encryption_key_id` varchar(100) DEFAULT NULL COMMENT '加密密钥ID',
  `compression_ratio` decimal(5,4) DEFAULT NULL COMMENT '压缩比',
  `upload_source` varchar(50) NOT NULL COMMENT '上传来源',
  `upload_ip` varchar(45) DEFAULT NULL COMMENT '上传IP',
  `uploaded_by` bigint unsigned NOT NULL COMMENT '上传人ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `related_id` varchar(100) DEFAULT NULL COMMENT '关联ID',
  `access_level` enum('public','private','restricted','confidential') NOT NULL DEFAULT 'private' COMMENT '访问级别',
  `retention_policy` enum('standard','extended','permanent','auto_delete') NOT NULL DEFAULT 'standard' COMMENT '保留策略',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `last_accessed_at` timestamp NULL DEFAULT NULL COMMENT '最后访问时间',
  `access_count` bigint unsigned NOT NULL DEFAULT '0' COMMENT '访问次数',
  `download_count` bigint unsigned NOT NULL DEFAULT '0' COMMENT '下载次数',
  `is_virus_scanned` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已病毒扫描',
  `virus_scan_result` varchar(100) DEFAULT NULL COMMENT '病毒扫描结果',
  `virus_scan_at` timestamp NULL DEFAULT NULL COMMENT '病毒扫描时间',
  `status` enum('uploading','processing','available','error','deleted','archived') NOT NULL DEFAULT 'uploading' COMMENT '文件状态',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件存储表';
