"""
视频管理器 - 多路视频流管理核心
支持80-100路视频流并发处理
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from .video_processor import VideoProcessor
from .frame_scheduler import FrameScheduler
from ..config.config_service import ConfigService
from ...repositories.camera_repository import CameraRepository
from ...core.logger import logger
from ...core.exceptions import VideoProcessingError


class VideoManager:
    """视频管理器 - 多路视频流管理"""
    
    def __init__(self, camera_repository: CameraRepository, config_service: ConfigService):
        self.camera_repository = camera_repository
        self.config_service = config_service
        
        # 视频处理器管理
        self.processors: Dict[int, VideoProcessor] = {}  # camera_id -> processor
        self.processor_status: Dict[int, str] = {}  # camera_id -> status
        
        # 帧调度器
        self.frame_scheduler = FrameScheduler()
        
        # 资源管理
        self.max_concurrent_streams = 100
        self.thread_pool = ThreadPoolExecutor(max_workers=50)
        
        # 状态管理
        self.is_running = False
        self.manager_task = None
        self.heartbeat_task = None
        
        logger.info("视频管理器初始化完成")
    
    async def start(self):
        """启动视频管理器"""
        if self.is_running:
            logger.warning("视频管理器已在运行中")
            return
        
        try:
            self.is_running = True
            
            # 启动帧调度器
            await self.frame_scheduler.start()
            
            # 加载摄像头配置并启动处理器
            await self.load_and_start_cameras()
            
            # 启动管理任务
            self.manager_task = asyncio.create_task(self._management_loop())
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            logger.info("视频管理器启动成功")
            
        except Exception as e:
            logger.error(f"视频管理器启动失败: {e}")
            await self.stop()
            raise VideoProcessingError(f"视频管理器启动失败: {e}")
    
    async def stop(self):
        """停止视频管理器"""
        if not self.is_running:
            return
        
        try:
            self.is_running = False
            
            # 停止所有处理器
            await self.stop_all_processors()
            
            # 停止调度器
            await self.frame_scheduler.stop()
            
            # 停止管理任务
            if self.manager_task:
                self.manager_task.cancel()
                try:
                    await self.manager_task
                except asyncio.CancelledError:
                    pass
            
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭线程池
            self.thread_pool.shutdown(wait=True)
            
            logger.info("视频管理器已停止")
            
        except Exception as e:
            logger.error(f"停止视频管理器时出错: {e}")
    
    async def load_and_start_cameras(self):
        """加载摄像头配置并启动处理器"""
        try:
            # 获取所有活跃的摄像头
            active_cameras = await self.camera_repository.get_active_cameras()
            
            logger.info(f"发现 {len(active_cameras)} 个活跃摄像头")
            
            # 检查并发限制
            if len(active_cameras) > self.max_concurrent_streams:
                logger.warning(f"摄像头数量 {len(active_cameras)} 超过并发限制 {self.max_concurrent_streams}")
                active_cameras = active_cameras[:self.max_concurrent_streams]
            
            # 启动处理器
            start_tasks = []
            for camera in active_cameras:
                task = self.start_camera_processor(camera.id)
                start_tasks.append(task)
            
            # 并发启动所有处理器
            results = await asyncio.gather(*start_tasks, return_exceptions=True)
            
            # 统计启动结果
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    camera_id = active_cameras[i].id
                    logger.error(f"启动摄像头 {camera_id} 处理器失败: {result}")
                else:
                    success_count += 1
            
            logger.info(f"成功启动 {success_count}/{len(active_cameras)} 个摄像头处理器")
            
        except Exception as e:
            logger.error(f"加载摄像头配置失败: {e}")
            raise
    
    async def start_camera_processor(self, camera_id: int) -> bool:
        """启动单个摄像头处理器"""
        try:
            if camera_id in self.processors:
                logger.warning(f"摄像头 {camera_id} 处理器已存在")
                return False
            
            # 获取摄像头分析配置
            camera_config = await self.camera_repository.get_camera_analysis_config(camera_id)
            if not camera_config:
                logger.error(f"无法获取摄像头 {camera_id} 配置")
                return False
            
            # 创建处理器
            processor = VideoProcessor(
                camera_id=camera_id,
                camera_config=camera_config,
                frame_scheduler=self.frame_scheduler,
                thread_pool=self.thread_pool
            )
            
            # 启动处理器
            await processor.start()
            
            self.processors[camera_id] = processor
            self.processor_status[camera_id] = "running"
            
            logger.info(f"摄像头 {camera_id} 处理器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动摄像头 {camera_id} 处理器失败: {e}")
            self.processor_status[camera_id] = "error"
            return False
    
    async def stop_camera_processor(self, camera_id: int) -> bool:
        """停止单个摄像头处理器"""
        try:
            if camera_id not in self.processors:
                logger.warning(f"摄像头 {camera_id} 处理器不存在")
                return False
            
            processor = self.processors[camera_id]
            await processor.stop()
            
            del self.processors[camera_id]
            self.processor_status[camera_id] = "stopped"
            
            logger.info(f"摄像头 {camera_id} 处理器已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止摄像头 {camera_id} 处理器失败: {e}")
            return False
    
    async def restart_camera_processor(self, camera_id: int) -> bool:
        """重启摄像头处理器"""
        try:
            # 先停止
            if camera_id in self.processors:
                await self.stop_camera_processor(camera_id)
            
            # 再启动
            return await self.start_camera_processor(camera_id)
            
        except Exception as e:
            logger.error(f"重启摄像头 {camera_id} 处理器失败: {e}")
            return False
    
    async def stop_all_processors(self):
        """停止所有处理器"""
        try:
            stop_tasks = []
            for camera_id in list(self.processors.keys()):
                task = self.stop_camera_processor(camera_id)
                stop_tasks.append(task)
            
            if stop_tasks:
                await asyncio.gather(*stop_tasks, return_exceptions=True)
            
            logger.info("所有处理器已停止")
            
        except Exception as e:
            logger.error(f"停止所有处理器时出错: {e}")
    
    async def get_processor_status(self) -> Dict[str, Any]:
        """获取处理器状态"""
        try:
            status = {
                "total_processors": len(self.processors),
                "running_count": 0,
                "error_count": 0,
                "stopped_count": 0,
                "processors": []
            }
            
            for camera_id, processor in self.processors.items():
                processor_info = {
                    "camera_id": camera_id,
                    "status": self.processor_status.get(camera_id, "unknown"),
                    "is_healthy": await processor.is_healthy(),
                    "frames_processed": processor.get_frames_processed(),
                    "error_count": processor.get_error_count(),
                    "last_frame_time": processor.get_last_frame_time()
                }
                
                status["processors"].append(processor_info)
                
                # 统计状态
                proc_status = processor_info["status"]
                if proc_status == "running":
                    status["running_count"] += 1
                elif proc_status == "error":
                    status["error_count"] += 1
                elif proc_status == "stopped":
                    status["stopped_count"] += 1
            
            return status
            
        except Exception as e:
            logger.error(f"获取处理器状态失败: {e}")
            return {"error": str(e)}
    
    async def _management_loop(self):
        """管理循环 - 监控处理器健康状态"""
        while self.is_running:
            try:
                # 检查处理器健康状态
                await self._check_processor_health()
                
                # 动态调整资源
                await self._adjust_resources()
                
                # 休眠一段时间
                await asyncio.sleep(30)  # 30秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"管理循环异常: {e}")
                await asyncio.sleep(10)
    
    async def _heartbeat_loop(self):
        """心跳循环 - 更新摄像头状态"""
        while self.is_running:
            try:
                # 批量更新摄像头心跳
                heartbeats = []
                for camera_id, processor in self.processors.items():
                    if await processor.is_healthy():
                        heartbeats.append({
                            "camera_id": camera_id,
                            "status": "online",
                            "timestamp": datetime.utcnow().isoformat()
                        })
                    else:
                        heartbeats.append({
                            "camera_id": camera_id,
                            "status": "error",
                            "timestamp": datetime.utcnow().isoformat()
                        })
                
                if heartbeats:
                    await self.camera_repository.batch_update_camera_heartbeat(heartbeats)
                
                await asyncio.sleep(60)  # 60秒更新一次心跳
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳循环异常: {e}")
                await asyncio.sleep(30)
    
    async def _check_processor_health(self):
        """检查处理器健康状态"""
        try:
            unhealthy_processors = []
            
            for camera_id, processor in self.processors.items():
                if not await processor.is_healthy():
                    unhealthy_processors.append(camera_id)
                    self.processor_status[camera_id] = "error"
            
            # 重启不健康的处理器
            for camera_id in unhealthy_processors:
                logger.warning(f"检测到摄像头 {camera_id} 处理器不健康，尝试重启")
                await self.restart_camera_processor(camera_id)
                
        except Exception as e:
            logger.error(f"检查处理器健康状态失败: {e}")
    
    async def _adjust_resources(self):
        """动态调整资源"""
        try:
            # 获取系统负载信息
            processor_count = len(self.processors)
            frame_queue_size = self.frame_scheduler.get_queue_size()
            
            # 如果队列积压过多，调整处理频率
            if frame_queue_size > 1000:  # 队列积压阈值
                logger.warning(f"帧队列积压严重: {frame_queue_size}，考虑降低处理频率")
                # 可以动态调整处理器的采样率
                
        except Exception as e:
            logger.error(f"调整资源失败: {e}")
    
    def get_active_processors(self) -> Dict[int, VideoProcessor]:
        """获取所有活跃的处理器"""
        return {k: v for k, v in self.processors.items() 
                if self.processor_status.get(k) == "running"}
    
    def get_processor(self, camera_id: int) -> Optional[VideoProcessor]:
        """获取指定摄像头的处理器"""
        return self.processors.get(camera_id) 