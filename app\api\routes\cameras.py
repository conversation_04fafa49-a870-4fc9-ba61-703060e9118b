"""
摄像头管理API路由
"""
import logging
from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db_session
from app.repositories.camera_repository import CameraRepository
from app.schemas.camera_schemas import (
    CameraResponse,
    CameraListResponse,
    CameraUpdateRequest,
    CameraStatsResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/cameras", tags=["摄像头管理"])


@router.get("/", response_model=CameraListResponse)
async def get_cameras(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    store_id: Optional[int] = Query(None, description="门店ID筛选"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    is_online: Optional[bool] = Query(None, description="是否在线筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取摄像头列表"""
    try:
        camera_repo = CameraRepository(db)
        
        # 构建查询条件
        filters = {}
        if store_id:
            filters["store_id"] = store_id
        if is_active is not None:
            filters["is_active"] = is_active
        if is_online is not None:
            filters["is_online"] = is_online
        if search:
            filters["search"] = search
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询摄像头
        cameras, total = await camera_repo.get_cameras_with_pagination(
            filters=filters,
            limit=page_size,
            offset=offset
        )
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return CameraListResponse(
            cameras=cameras,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"获取摄像头列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取摄像头列表失败: {str(e)}"
        )


@router.get("/{camera_id}", response_model=CameraResponse)
async def get_camera_detail(
    camera_id: int = Path(..., description="摄像头ID"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取摄像头详情"""
    try:
        camera_repo = CameraRepository(db)
        camera = await camera_repo.get_by_id(camera_id)
        
        if not camera:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"摄像头 {camera_id} 不存在"
            )
        
        return CameraResponse.from_orm(camera)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取摄像头详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取摄像头详情失败: {str(e)}"
        )


@router.put("/{camera_id}/status")
async def update_camera_status(
    camera_id: int = Path(..., description="摄像头ID"),
    is_active: bool = Query(..., description="是否启用"),
    db: AsyncSession = Depends(get_db_session)
):
    """更新摄像头启用状态"""
    try:
        camera_repo = CameraRepository(db)
        camera = await camera_repo.get_by_id(camera_id)
        
        if not camera:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"摄像头 {camera_id} 不存在"
            )
        
        # 更新状态
        camera.is_active = is_active
        await camera_repo.update(camera)
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": f"摄像头 {camera_id} 状态已更新为 {'启用' if is_active else '禁用'}",
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新摄像头状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新摄像头状态失败: {str(e)}"
        )


@router.get("/store/{store_id}", response_model=CameraListResponse)
async def get_store_cameras(
    store_id: int = Path(..., description="门店ID"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取指定门店的摄像头列表"""
    try:
        camera_repo = CameraRepository(db)
        
        filters = {"store_id": store_id}
        if is_active is not None:
            filters["is_active"] = is_active
        
        cameras, total = await camera_repo.get_cameras_with_pagination(
            filters=filters,
            limit=1000,  # 获取门店所有摄像头
            offset=0
        )
        
        return CameraListResponse(
            cameras=cameras,
            total=total,
            page=1,
            page_size=total,
            total_pages=1
        )
        
    except Exception as e:
        logger.error(f"获取门店摄像头失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取门店摄像头失败: {str(e)}"
        )


@router.get("/stats/summary", response_model=CameraStatsResponse)
async def get_camera_stats(
    store_id: Optional[int] = Query(None, description="门店ID筛选"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取摄像头统计信息"""
    try:
        camera_repo = CameraRepository(db)
        
        filters = {}
        if store_id:
            filters["store_id"] = store_id
        
        stats = await camera_repo.get_camera_statistics(filters)
        
        return CameraStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"获取摄像头统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取摄像头统计失败: {str(e)}"
        )


@router.post("/{camera_id}/test-connection")
async def test_camera_connection(
    camera_id: int = Path(..., description="摄像头ID"),
    db: AsyncSession = Depends(get_db_session)
):
    """测试摄像头连接"""
    try:
        camera_repo = CameraRepository(db)
        camera = await camera_repo.get_by_id(camera_id)
        
        if not camera:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"摄像头 {camera_id} 不存在"
            )
        
        # 这里应该添加实际的连接测试逻辑
        # 现在只是模拟测试结果
        test_result = {
            "camera_id": camera_id,
            "camera_name": camera.name,
            "rtsp_url": camera.rtsp_url,
            "connection_status": "success",  # 模拟连接成功
            "response_time": 150,  # 模拟响应时间(ms)
            "test_time": datetime.now().isoformat(),
            "message": "摄像头连接测试成功"
        }
        
        return test_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试摄像头连接失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试摄像头连接失败: {str(e)}"
        )


@router.get("/online/list")
async def get_online_cameras(
    store_id: Optional[int] = Query(None, description="门店ID筛选"),
    db: AsyncSession = Depends(get_db_session)
):
    """获取在线摄像头列表"""
    try:
        camera_repo = CameraRepository(db)
        
        filters = {"is_online": True}
        if store_id:
            filters["store_id"] = store_id
        
        cameras, total = await camera_repo.get_cameras_with_pagination(
            filters=filters,
            limit=1000,
            offset=0
        )
        
        return {
            "online_cameras": cameras,
            "total_online": total,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取在线摄像头失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取在线摄像头失败: {str(e)}"
        )


@router.post("/{camera_id}/snapshot")
async def capture_snapshot(
    camera_id: int = Path(..., description="摄像头ID"),
    db: AsyncSession = Depends(get_db_session)
):
    """抓取摄像头快照"""
    try:
        camera_repo = CameraRepository(db)
        camera = await camera_repo.get_by_id(camera_id)
        
        if not camera:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"摄像头 {camera_id} 不存在"
            )
        
        # 这里应该添加实际的快照抓取逻辑
        # 现在只是模拟返回结果
        snapshot_info = {
            "camera_id": camera_id,
            "camera_name": camera.name,
            "snapshot_url": f"/api/v1/cameras/{camera_id}/snapshot.jpg",
            "capture_time": datetime.now().isoformat(),
            "message": "快照抓取成功"
        }
        
        return snapshot_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"抓取摄像头快照失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"抓取摄像头快照失败: {str(e)}"
        ) 