# 应用环境配置
ENVIRONMENT=development
DEBUG=true
PROJECT_NAME=智能视频监控预警系统
VERSION=1.0.0

# 服务器配置
HOST=0.0.0.0
PORT=8000
RELOAD=true

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=video_monitor
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=20
REDIS_TIMEOUT=5

# MinIO对象存储配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_SECURE=false
MINIO_BUCKET_NAME=video-monitor

# AI配置
AI_API_KEY=your_dashscope_api_key
AI_MODEL_NAME=qwen-vl-plus
AI_MAX_RETRIES=3
AI_TIMEOUT=30
AI_MAX_CONCURRENT=50

# 视频处理配置
VIDEO_MAX_STREAMS=100
VIDEO_FRAME_RATE=2
VIDEO_BUFFER_SIZE=30
VIDEO_TIMEOUT=30
VIDEO_RECONNECT_INTERVAL=5
VIDEO_MAX_RECONNECTS=3

# 性能优化配置
MOTION_DETECTION_THRESHOLD=5000
MOTION_DETECTION_BLUR_SIZE=15
MOTION_DETECTION_MIN_AREA=500
FRAME_AGGREGATION_WINDOW=5
FRAME_AGGREGATION_MAX_FRAMES=10
BATCH_PROCESSING_SIZE=5
BATCH_PROCESSING_TIMEOUT=10

# 预警配置  
ALERT_BUFFER_SIZE=1000
ALERT_BATCH_SIZE=50
ALERT_FLUSH_INTERVAL=5

# WebSocket配置
WEBSOCKET_MAX_CONNECTIONS=1000
WEBSOCKET_HEARTBEAT_INTERVAL=30
WEBSOCKET_RECONNECT_INTERVAL=5

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10
LOG_ROTATION=1 day

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=1440
CORS_ORIGINS=*

# 监控配置
HEALTH_CHECK_INTERVAL=30
RESOURCE_MONITOR_INTERVAL=60
METRIC_COLLECTION_ENABLED=true

# 开发配置（仅开发环境）
ENABLE_SWAGGER=true
ENABLE_REDOC=true
ENABLE_DEBUG_ENDPOINTS=true 