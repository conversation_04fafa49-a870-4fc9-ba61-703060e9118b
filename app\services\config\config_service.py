"""
配置服务 - 配置驱动架构核心
提供摄像头配置、AI规则、提示词模板的统一管理
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import asyncio

from ...repositories import (
    CameraRepository, AnalysisRuleRepository, 
    AnomalyBehaviorRepository, PromptTemplateRepository,
    RuleBehaviorMappingRepository
)
from ...core.logger import logger
from ...core.exceptions import ConfigurationError


class ConfigService:
    """配置服务 - 配置驱动架构核心"""
    
    def __init__(self, camera_repo: CameraRepository, 
                 analysis_rule_repo: AnalysisRuleRepository,
                 anomaly_behavior_repo: AnomalyBehaviorRepository,
                 prompt_template_repo: PromptTemplateRepository,
                 rule_behavior_mapping_repo: RuleBehaviorMappingRepository):
        
        self.camera_repo = camera_repo
        self.analysis_rule_repo = analysis_rule_repo
        self.anomaly_behavior_repo = anomaly_behavior_repo
        self.prompt_template_repo = prompt_template_repo
        self.rule_behavior_mapping_repo = rule_behavior_mapping_repo
        
        # 配置缓存
        self.config_cache = {}
        self.cache_expiry = 300  # 5分钟缓存过期
        self.last_refresh = {}
        
        logger.info("配置服务初始化完成")
    
    async def get_camera_analysis_config(self, camera_id: int) -> Optional[Dict[str, Any]]:
        """获取摄像头的完整分析配置"""
        try:
            cache_key = f"camera_config_{camera_id}"
            
            # 检查缓存
            if await self._is_cache_valid(cache_key):
                return self.config_cache.get(cache_key)
            
            # 获取摄像头基本信息
            camera = await self.camera_repo.get_by_id(camera_id)
            if not camera:
                logger.warning(f"摄像头 {camera_id} 不存在")
                return None
            
            # 获取分析规则
            rules = await self.analysis_rule_repo.get_camera_rules(camera_id)
            if not rules:
                logger.warning(f"摄像头 {camera_id} 没有配置分析规则")
                return None
            
            # 构建完整配置
            config = {
                "camera_id": camera_id,
                "camera_name": camera.name,
                "rtsp_url": camera.rtsp_url,
                "location": camera.location,
                "store_id": camera.store_id,
                "analysis_enabled": camera.analysis_enabled,
                "motion_threshold": camera.motion_threshold or 0.01,
                "motion_min_area": camera.motion_min_area or 500,
                "rules": []
            }
            
            # 处理每个规则
            for rule in rules:
                rule_config = await self._build_rule_config(rule)
                if rule_config:
                    config["rules"].append(rule_config)
            
            # 缓存配置
            self.config_cache[cache_key] = config
            self.last_refresh[cache_key] = datetime.utcnow()
            
            logger.debug(f"摄像头 {camera_id} 配置已加载: {len(config['rules'])} 个规则")
            
            return config
            
        except Exception as e:
            logger.error(f"获取摄像头 {camera_id} 配置失败: {e}")
            raise ConfigurationError(f"获取摄像头配置失败: {e}")
    
    async def _build_rule_config(self, rule) -> Optional[Dict[str, Any]]:
        """构建单个规则的配置"""
        try:
            rule_config = {
                "rule_id": rule.id,
                "rule_name": rule.name,
                "confidence_threshold": rule.confidence_threshold,
                "consecutive_frames": rule.consecutive_frames,
                "time_window_seconds": rule.time_window_seconds,
                "max_frames_per_batch": rule.max_frames_per_batch,
                "alert_interval_seconds": rule.alert_interval_seconds,
                "behaviors": []
            }
            
            # 获取规则关联的异常行为
            mappings = await self.rule_behavior_mapping_repo.get_rule_mappings(rule.id)
            
            for mapping in mappings:
                behavior = await self.anomaly_behavior_repo.get_by_id(mapping.behavior_id)
                if not behavior:
                    continue
                
                # 构建行为配置
                behavior_config = {
                    "behavior_id": behavior.id,
                    "behavior_code": behavior.code,
                    "behavior_name": behavior.name,
                    "description": behavior.description,
                    "keywords": behavior.ai_keywords.split(",") if behavior.ai_keywords else [],
                    "severity": behavior.severity,
                    "base_confidence_threshold": behavior.confidence_threshold,
                    "override_confidence_threshold": mapping.confidence_threshold,  # 规则级别覆盖
                    "override_consecutive_frames": mapping.consecutive_frames,
                    "prompt_template": None
                }
                
                # 获取提示词模板
                template = await self.prompt_template_repo.get_behavior_template(
                    behavior.id, scenario=rule.name
                )
                
                if template:
                    behavior_config["prompt_template"] = {
                        "template_id": template.id,
                        "template_code": template.code,
                        "prompt_text": template.prompt_text,
                        "variables": template.variables.split(",") if template.variables else [],
                        "model_type": template.model_type
                    }
                
                rule_config["behaviors"].append(behavior_config)
            
            return rule_config if rule_config["behaviors"] else None
            
        except Exception as e:
            logger.error(f"构建规则配置失败: {e}")
            return None
    
    async def get_ai_prompt_for_camera(self, camera_id: int) -> Optional[str]:
        """为摄像头构建综合AI分析提示词"""
        try:
            config = await self.get_camera_analysis_config(camera_id)
            if not config:
                return None
            
            # 收集所有行为的关键词和提示词
            all_behaviors = []
            behavior_descriptions = []
            
            for rule in config["rules"]:
                for behavior in rule["behaviors"]:
                    behavior_info = {
                        "name": behavior["behavior_name"],
                        "keywords": behavior["keywords"],
                        "description": behavior["description"],
                        "severity": behavior["severity"]
                    }
                    
                    if behavior["prompt_template"]:
                        behavior_info["specific_prompt"] = behavior["prompt_template"]["prompt_text"]
                    
                    all_behaviors.append(behavior_info)
            
            # 构建综合提示词
            prompt = self._build_comprehensive_prompt(
                camera_name=config["camera_name"],
                location=config["location"],
                behaviors=all_behaviors
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"构建摄像头 {camera_id} AI提示词失败: {e}")
            return None
    
    def _build_comprehensive_prompt(self, camera_name: str, location: str, 
                                  behaviors: List[Dict[str, Any]]) -> str:
        """构建综合检测提示词"""
        try:
            # 基础提示词
            base_prompt = f"""
你是一个专业的视频监控AI分析助手。你正在分析来自"{camera_name}"摄像头的视频画面，该摄像头位于{location}。

请仔细分析提供的视频帧，检测以下异常行为：

"""
            
            # 添加每个行为的描述
            for i, behavior in enumerate(behaviors, 1):
                behavior_prompt = f"""
{i}. {behavior['name']} (严重等级: {behavior['severity']})
   描述: {behavior['description']}
   关键特征: {', '.join(behavior['keywords'])}
"""
                if behavior.get('specific_prompt'):
                    behavior_prompt += f"   详细检测要求: {behavior['specific_prompt']}\n"
                
                base_prompt += behavior_prompt
            
            # 输出格式要求
            output_format = """

分析要求：
1. 仔细观察视频帧中的人员行为和场景变化
2. 识别是否存在上述任何异常行为
3. 如果检测到异常，请提供具体的行为类型、置信度和详细描述
4. 如果未检测到异常，请明确说明"未检测到异常行为"

请以JSON格式返回分析结果：
{
    "has_anomaly": true/false,
    "detected_behaviors": [
        {
            "behavior_code": "行为代码",
            "behavior_name": "行为名称", 
            "confidence": 0.0-1.0,
            "description": "详细描述检测到的异常情况",
            "location": "异常发生的大概位置"
        }
    ],
    "analysis_summary": "整体分析总结"
}
"""
            
            return base_prompt + output_format
            
        except Exception as e:
            logger.error(f"构建综合提示词失败: {e}")
            return "请分析视频帧中是否存在异常行为。"
    
    async def refresh_camera_config(self, camera_id: int):
        """刷新指定摄像头的配置缓存"""
        try:
            cache_key = f"camera_config_{camera_id}"
            
            # 清除缓存
            if cache_key in self.config_cache:
                del self.config_cache[cache_key]
            if cache_key in self.last_refresh:
                del self.last_refresh[cache_key]
            
            # 重新加载
            await self.get_camera_analysis_config(camera_id)
            
            logger.info(f"摄像头 {camera_id} 配置缓存已刷新")
            
        except Exception as e:
            logger.error(f"刷新摄像头 {camera_id} 配置失败: {e}")
    
    async def refresh_all_configs(self):
        """刷新所有配置缓存"""
        try:
            # 清空所有缓存
            self.config_cache.clear()
            self.last_refresh.clear()
            
            logger.info("所有配置缓存已清空")
            
        except Exception as e:
            logger.error(f"刷新所有配置失败: {e}")
    
    async def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        try:
            if cache_key not in self.config_cache:
                return False
            
            if cache_key not in self.last_refresh:
                return False
            
            last_refresh = self.last_refresh[cache_key]
            time_diff = (datetime.utcnow() - last_refresh).total_seconds()
            
            return time_diff < self.cache_expiry
            
        except Exception:
            return False
    
    async def validate_camera_config(self, camera_id: int) -> Dict[str, Any]:
        """验证摄像头配置的完整性"""
        try:
            validation_result = {
                "camera_id": camera_id,
                "is_valid": True,
                "errors": [],
                "warnings": []
            }
            
            # 检查摄像头是否存在
            camera = await self.camera_repo.get_by_id(camera_id)
            if not camera:
                validation_result["is_valid"] = False
                validation_result["errors"].append("摄像头不存在")
                return validation_result
            
            # 检查是否启用分析
            if not camera.analysis_enabled:
                validation_result["warnings"].append("摄像头未启用AI分析")
            
            # 检查RTSP地址
            if not camera.rtsp_url:
                validation_result["is_valid"] = False
                validation_result["errors"].append("未配置RTSP地址")
            
            # 检查分析规则
            rules = await self.analysis_rule_repo.get_camera_rules(camera_id)
            if not rules:
                validation_result["is_valid"] = False
                validation_result["errors"].append("未配置分析规则")
                return validation_result
            
            # 检查每个规则的行为映射
            for rule in rules:
                mappings = await self.rule_behavior_mapping_repo.get_rule_mappings(rule.id)
                if not mappings:
                    validation_result["warnings"].append(f"规则 {rule.name} 未关联异常行为")
                    continue
                
                # 检查行为的提示词模板
                for mapping in mappings:
                    behavior = await self.anomaly_behavior_repo.get_by_id(mapping.behavior_id)
                    if behavior:
                        template = await self.prompt_template_repo.get_behavior_template(
                            behavior.id, scenario=rule.name
                        )
                        if not template:
                            validation_result["warnings"].append(
                                f"行为 {behavior.name} 未配置提示词模板"
                            )
            
            return validation_result
            
        except Exception as e:
            logger.error(f"验证摄像头 {camera_id} 配置失败: {e}")
            return {
                "camera_id": camera_id,
                "is_valid": False,
                "errors": [f"验证过程异常: {e}"],
                "warnings": []
            }
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取配置缓存统计信息"""
        return {
            "cached_configs": len(self.config_cache),
            "cache_expiry_seconds": self.cache_expiry,
            "cache_keys": list(self.config_cache.keys()),
            "last_refresh_times": {
                k: v.isoformat() for k, v in self.last_refresh.items()
            }
        } 