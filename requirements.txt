# FastAPI 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据验证和设置
pydantic==2.5.0
pydantic-settings==2.0.3

# 异步HTTP客户端
httpx==0.25.2
aiohttp==3.9.0

# 数据库相关
aiomysql==0.2.0
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1

# Redis缓存
aioredis==2.0.1

# 对象存储
minio==7.2.0

# 视频处理
opencv-python==********
numpy==1.25.2
pillow==10.1.0

# AI模型集成
dashscope==1.17.0  # 阿里云通义千问SDK

# 工具库
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dateutil==2.8.2
pytz==2023.3

# 日志和监控
structlog==23.2.0
loguru==0.7.2

# 配置管理
pyyaml==6.0.1
python-dotenv==1.0.0

# 异步任务队列
celery==5.3.4
redis==5.0.1

# 数据序列化
orjson==3.9.10

# 文件处理
aiofiles==23.2.1

# 时间处理
pendulum==2.1.2

# UUID生成
shortuuid==1.0.11

# 加密
cryptography==41.0.7

# WebSocket支持
websockets==12.0 