"""
视频处理器 - 单路视频流处理核心
实现三层性能优化架构：预过滤 -> 聚合 -> AI分析
"""

import asyncio
import cv2
import numpy as np
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import time

from .frame_buffer import Frame<PERSON>uffer
from .frame_scheduler import FrameScheduler
from ..analysis.motion_detector import MotionDetector
from ..analysis.frame_aggregator import FrameAggregator
from ...core.logger import logger
from ...core.exceptions import VideoProcessingError


class VideoProcessor:
    """视频处理器 - 单路视频流处理"""
    
    def __init__(self, camera_id: int, camera_config: Dict[str, Any], 
                 frame_scheduler: FrameScheduler, thread_pool: ThreadPoolExecutor):
        self.camera_id = camera_id
        self.camera_config = camera_config
        self.frame_scheduler = frame_scheduler
        self.thread_pool = thread_pool
        
        # 视频连接配置
        self.rtsp_url = camera_config.get("rtsp_url", "")
        self.video_capture = None
        
        # 帧缓冲区
        self.frame_buffer = FrameBuffer(max_size=100)
        
        # 三层性能优化组件
        self.motion_detector = MotionDetector(
            threshold=camera_config.get("motion_threshold", 0.01),
            min_area=camera_config.get("motion_min_area", 500)
        )
        self.frame_aggregator = FrameAggregator(
            time_window=camera_config.get("time_window_seconds", 10),
            max_frames=camera_config.get("max_frames_per_batch", 5)
        )
        
        # 处理状态
        self.is_running = False
        self.capture_task = None
        self.process_task = None
        
        # 统计信息
        self.frames_captured = 0
        self.frames_processed = 0
        self.frames_filtered = 0  # 被预过滤丢弃的帧
        self.error_count = 0
        self.last_frame_time = None
        self.last_error_time = None
        
        # 性能监控
        self.capture_fps = 0
        self.process_fps = 0
        self.last_fps_update = time.time()
        self.fps_counter = 0
        
        logger.info(f"摄像头 {camera_id} 处理器初始化完成")
    
    async def start(self):
        """启动视频处理器"""
        if self.is_running:
            logger.warning(f"摄像头 {self.camera_id} 处理器已在运行")
            return
        
        try:
            # 初始化视频连接
            if not await self._initialize_video_capture():
                raise VideoProcessingError(f"无法连接摄像头 {self.camera_id}")
            
            self.is_running = True
            
            # 启动采集和处理任务
            self.capture_task = asyncio.create_task(self._capture_loop())
            self.process_task = asyncio.create_task(self._process_loop())
            
            logger.info(f"摄像头 {self.camera_id} 处理器启动成功")
            
        except Exception as e:
            logger.error(f"摄像头 {self.camera_id} 处理器启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止视频处理器"""
        if not self.is_running:
            return
        
        try:
            self.is_running = False
            
            # 停止任务
            if self.capture_task:
                self.capture_task.cancel()
                try:
                    await self.capture_task
                except asyncio.CancelledError:
                    pass
            
            if self.process_task:
                self.process_task.cancel()
                try:
                    await self.process_task
                except asyncio.CancelledError:
                    pass
            
            # 释放视频资源
            if self.video_capture:
                self.video_capture.release()
                self.video_capture = None
            
            logger.info(f"摄像头 {self.camera_id} 处理器已停止")
            
        except Exception as e:
            logger.error(f"停止摄像头 {self.camera_id} 处理器时出错: {e}")
    
    async def _initialize_video_capture(self) -> bool:
        """初始化视频连接"""
        try:
            # 在线程池中执行视频连接（避免阻塞）
            loop = asyncio.get_event_loop()
            self.video_capture = await loop.run_in_executor(
                self.thread_pool, self._create_video_capture
            )
            
            if self.video_capture and self.video_capture.isOpened():
                # 设置缓冲区大小
                self.video_capture.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                # 获取视频信息
                fps = self.video_capture.get(cv2.CAP_PROP_FPS)
                width = int(self.video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(self.video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                logger.info(f"摄像头 {self.camera_id} 连接成功: {width}x{height}@{fps}fps")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"初始化摄像头 {self.camera_id} 连接失败: {e}")
            return False
    
    def _create_video_capture(self) -> Optional[cv2.VideoCapture]:
        """创建视频捕获对象"""
        try:
            cap = cv2.VideoCapture(self.rtsp_url)
            
            # 设置超时和重连参数
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)  # 5秒连接超时
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)  # 3秒读取超时
            
            return cap
            
        except Exception as e:
            logger.error(f"创建视频捕获对象失败: {e}")
            return None
    
    async def _capture_loop(self):
        """视频采集循环"""
        consecutive_failures = 0
        max_failures = 10
        
        while self.is_running:
            try:
                # 在线程池中读取帧
                loop = asyncio.get_event_loop()
                ret, frame = await loop.run_in_executor(
                    self.thread_pool, self._read_frame
                )
                
                if ret and frame is not None:
                    # 成功读取帧
                    consecutive_failures = 0
                    
                    # 添加到缓冲区
                    frame_data = {
                        "camera_id": self.camera_id,
                        "frame": frame,
                        "timestamp": datetime.utcnow(),
                        "frame_id": self.frames_captured
                    }
                    
                    await self.frame_buffer.put(frame_data)
                    
                    self.frames_captured += 1
                    self.last_frame_time = datetime.utcnow()
                    
                    # 更新FPS统计
                    self._update_capture_fps()
                    
                else:
                    # 读取失败
                    consecutive_failures += 1
                    logger.warning(f"摄像头 {self.camera_id} 读取帧失败，连续失败次数: {consecutive_failures}")
                    
                    if consecutive_failures >= max_failures:
                        logger.error(f"摄像头 {self.camera_id} 连续读取失败次数过多，尝试重连")
                        await self._reconnect_video()
                        consecutive_failures = 0
                    
                    await asyncio.sleep(0.1)  # 短暂休眠后重试
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.error_count += 1
                self.last_error_time = datetime.utcnow()
                logger.error(f"摄像头 {self.camera_id} 采集循环异常: {e}")
                await asyncio.sleep(1)
    
    async def _process_loop(self):
        """帧处理循环 - 三层性能优化"""
        while self.is_running:
            try:
                # 从缓冲区获取帧
                frame_data = await self.frame_buffer.get(timeout=1.0)
                if not frame_data:
                    continue
                
                # 第一层：预过滤（运动检测）
                if not await self._motion_pre_filter(frame_data):
                    self.frames_filtered += 1
                    continue
                
                # 第二层：帧聚合
                aggregated_frames = await self._aggregate_frames(frame_data)
                if not aggregated_frames:
                    continue
                
                # 第三层：提交AI分析
                await self._submit_for_ai_analysis(aggregated_frames)
                
                self.frames_processed += 1
                self._update_process_fps()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.error_count += 1
                logger.error(f"摄像头 {self.camera_id} 处理循环异常: {e}")
                await asyncio.sleep(0.1)
    
    def _read_frame(self) -> tuple:
        """读取视频帧（在线程池中执行）"""
        try:
            if self.video_capture and self.video_capture.isOpened():
                return self.video_capture.read()
            return False, None
            
        except Exception as e:
            logger.error(f"读取摄像头 {self.camera_id} 帧失败: {e}")
            return False, None
    
    async def _motion_pre_filter(self, frame_data: Dict[str, Any]) -> bool:
        """第一层：运动检测预过滤"""
        try:
            frame = frame_data["frame"]
            
            # 在线程池中执行运动检测
            loop = asyncio.get_event_loop()
            has_motion = await loop.run_in_executor(
                self.thread_pool, self.motion_detector.detect_motion, frame
            )
            
            if has_motion:
                logger.debug(f"摄像头 {self.camera_id} 检测到运动")
            
            return has_motion
            
        except Exception as e:
            logger.error(f"运动检测失败: {e}")
            return True  # 出错时不过滤
    
    async def _aggregate_frames(self, frame_data: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """第二层：帧聚合"""
        try:
            # 添加帧到聚合器
            self.frame_aggregator.add_frame(frame_data)
            
            # 检查是否可以输出聚合帧
            if self.frame_aggregator.should_output():
                return self.frame_aggregator.get_aggregated_frames()
            
            return None
            
        except Exception as e:
            logger.error(f"帧聚合失败: {e}")
            return None
    
    async def _submit_for_ai_analysis(self, aggregated_frames: List[Dict[str, Any]]):
        """第三层：提交AI分析"""
        try:
            # 构建分析任务
            analysis_task = {
                "camera_id": self.camera_id,
                "camera_config": self.camera_config,
                "frames": aggregated_frames,
                "timestamp": datetime.utcnow(),
                "task_id": f"{self.camera_id}_{int(time.time())}"
            }
            
            # 提交到帧调度器
            await self.frame_scheduler.submit_analysis_task(analysis_task)
            
            logger.debug(f"摄像头 {self.camera_id} 提交 {len(aggregated_frames)} 帧进行AI分析")
            
        except Exception as e:
            logger.error(f"提交AI分析失败: {e}")
    
    async def _reconnect_video(self):
        """重新连接视频"""
        try:
            logger.info(f"摄像头 {self.camera_id} 开始重连")
            
            # 释放旧连接
            if self.video_capture:
                self.video_capture.release()
                self.video_capture = None
            
            # 等待一段时间后重连
            await asyncio.sleep(2)
            
            # 重新初始化连接
            if await self._initialize_video_capture():
                logger.info(f"摄像头 {self.camera_id} 重连成功")
            else:
                logger.error(f"摄像头 {self.camera_id} 重连失败")
                
        except Exception as e:
            logger.error(f"摄像头 {self.camera_id} 重连过程异常: {e}")
    
    def _update_capture_fps(self):
        """更新采集FPS统计"""
        self.fps_counter += 1
        current_time = time.time()
        
        if current_time - self.last_fps_update >= 1.0:  # 每秒更新一次
            self.capture_fps = self.fps_counter / (current_time - self.last_fps_update)
            self.fps_counter = 0
            self.last_fps_update = current_time
    
    def _update_process_fps(self):
        """更新处理FPS统计"""
        # 处理FPS基于处理过的帧数计算
        pass
    
    async def is_healthy(self) -> bool:
        """检查处理器健康状态"""
        try:
            # 检查是否运行中
            if not self.is_running:
                return False
            
            # 检查视频连接
            if not self.video_capture or not self.video_capture.isOpened():
                return False
            
            # 检查最近是否有帧
            if self.last_frame_time:
                time_since_last_frame = datetime.utcnow() - self.last_frame_time
                if time_since_last_frame > timedelta(seconds=30):  # 30秒没有帧
                    return False
            
            # 检查错误率
            if self.frames_captured > 0:
                error_rate = self.error_count / self.frames_captured
                if error_rate > 0.1:  # 错误率超过10%
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查摄像头 {self.camera_id} 健康状态失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        return {
            "camera_id": self.camera_id,
            "frames_captured": self.frames_captured,
            "frames_processed": self.frames_processed,
            "frames_filtered": self.frames_filtered,
            "error_count": self.error_count,
            "capture_fps": self.capture_fps,
            "process_fps": self.process_fps,
            "last_frame_time": self.last_frame_time.isoformat() if self.last_frame_time else None,
            "last_error_time": self.last_error_time.isoformat() if self.last_error_time else None,
            "buffer_size": self.frame_buffer.size(),
            "filter_efficiency": self.frames_filtered / max(self.frames_captured, 1) * 100
        }
    
    def get_frames_processed(self) -> int:
        """获取已处理帧数"""
        return self.frames_processed
    
    def get_error_count(self) -> int:
        """获取错误次数"""
        return self.error_count
    
    def get_last_frame_time(self) -> Optional[datetime]:
        """获取最后一帧时间"""
        return self.last_frame_time 