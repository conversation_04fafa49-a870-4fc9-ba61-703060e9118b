"""
门店数据模型
"""

from typing import List, Optional
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime
from sqlalchemy.orm import relationship, Mapped, mapped_column
from .base import BaseModel


class Store(BaseModel):
    """门店模型"""
    
    __tablename__ = "stores"
    
    # 基础信息
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True, comment="门店名称")
    code: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True, comment="门店编码")
    
    # 联系信息
    contact_person: Mapped[Optional[str]] = mapped_column(String(50), comment="联系人")
    contact_phone: Mapped[Optional[str]] = mapped_column(String(20), comment="联系电话")
    
    # 地址信息
    address: Mapped[Optional[str]] = mapped_column(Text, comment="详细地址")
    city: Mapped[Optional[str]] = mapped_column(String(50), comment="城市")
    district: Mapped[Optional[str]] = mapped_column(String(50), comment="区域")
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(<PERSON>olean, default=True, nullable=False, comment="是否启用")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="门店描述")
    
    # 关联关系
    cameras: Mapped[List["Camera"]] = relationship(
        "Camera", 
        back_populates="store",
        cascade="all, delete-orphan"
    )
    analysis_rules = relationship("AnalysisRule", back_populates="store", cascade="all, delete-orphan")
    alert_events: Mapped[List["AlertEvent"]] = relationship(
        "AlertEvent", 
        back_populates="store"
    )
    
    def __repr__(self) -> str:
        return f"<Store(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    @property
    def active_cameras_count(self) -> int:
        """活跃摄像头数量"""
        return len([cam for cam in self.cameras if cam.is_active])
    
    @property
    def total_cameras_count(self) -> int:
        """总摄像头数量"""
        return len(self.cameras) 