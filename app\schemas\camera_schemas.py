"""
摄像头相关数据模式
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class CameraBase(BaseModel):
    """摄像头基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="摄像头名称")
    code: str = Field(..., min_length=1, max_length=50, description="摄像头编码")
    store_id: int = Field(..., gt=0, description="所属门店ID")
    rtsp_url: str = Field(..., min_length=1, max_length=500, description="RTSP地址")
    location: Optional[str] = Field(None, max_length=100, description="安装位置")
    description: Optional[str] = Field(None, description="摄像头描述")


class CameraCreate(CameraBase):
    """创建摄像头模式"""
    username: Optional[str] = Field(None, max_length=50, description="用户名")
    password: Optional[str] = Field(None, max_length=100, description="密码")
    ip_address: Optional[str] = Field(None, max_length=45, description="IP地址")
    port: Optional[int] = Field(None, ge=1, le=65535, description="端口号")
    resolution: Optional[str] = Field(None, max_length=20, description="分辨率")
    fps: Optional[int] = Field(None, ge=1, le=60, description="帧率")
    is_active: bool = Field(True, description="是否启用")


class CameraUpdate(BaseModel):
    """更新摄像头模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="摄像头名称")
    rtsp_url: Optional[str] = Field(None, min_length=1, max_length=500, description="RTSP地址")
    location: Optional[str] = Field(None, max_length=100, description="安装位置")
    description: Optional[str] = Field(None, description="摄像头描述")
    username: Optional[str] = Field(None, max_length=50, description="用户名")
    password: Optional[str] = Field(None, max_length=100, description="密码")
    ip_address: Optional[str] = Field(None, max_length=45, description="IP地址")
    port: Optional[int] = Field(None, ge=1, le=65535, description="端口号")
    resolution: Optional[str] = Field(None, max_length=20, description="分辨率")
    fps: Optional[int] = Field(None, ge=1, le=60, description="帧率")
    is_active: Optional[bool] = Field(None, description="是否启用")


class CameraUpdateRequest(BaseModel):
    """更新摄像头请求模式"""
    is_active: bool = Field(..., description="是否启用")
    notes: Optional[str] = Field(None, description="更新说明")


class CameraResponse(CameraBase):
    """摄像头响应模式"""
    id: int = Field(..., description="摄像头ID")
    username: Optional[str] = Field(None, description="用户名")
    ip_address: Optional[str] = Field(None, description="IP地址")
    port: Optional[int] = Field(None, description="端口号")
    resolution: Optional[str] = Field(None, description="分辨率")
    fps: Optional[int] = Field(None, description="帧率")
    is_active: bool = Field(..., description="是否启用")
    is_online: bool = Field(..., description="是否在线")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 关联数据
    store_name: Optional[str] = Field(None, description="门店名称")
    
    class Config:
        from_attributes = True


class CameraListResponse(BaseModel):
    """摄像头列表响应模式"""
    cameras: List[CameraResponse] = Field(..., description="摄像头列表")
    total: int = Field(..., ge=0, description="总数量")
    page: int = Field(..., ge=1, description="当前页码")
    page_size: int = Field(..., ge=1, description="每页数量")
    total_pages: int = Field(..., ge=0, description="总页数")


class CameraStatsResponse(BaseModel):
    """摄像头统计响应模式"""
    total_cameras: int = Field(..., ge=0, description="总摄像头数")
    active_cameras: int = Field(..., ge=0, description="启用的摄像头数")
    online_cameras: int = Field(..., ge=0, description="在线摄像头数")
    offline_cameras: int = Field(..., ge=0, description="离线摄像头数")
    by_store: List[Dict[str, Any]] = Field(..., description="按门店统计")
    timestamp: datetime = Field(..., description="统计时间")


class CameraConnectionTest(BaseModel):
    """摄像头连接测试模式"""
    camera_id: int = Field(..., description="摄像头ID")
    camera_name: str = Field(..., description="摄像头名称")
    rtsp_url: str = Field(..., description="RTSP地址")
    connection_status: str = Field(..., description="连接状态")
    response_time: Optional[int] = Field(None, description="响应时间(ms)")
    test_time: datetime = Field(..., description="测试时间")
    message: str = Field(..., description="测试结果消息")
    error_details: Optional[str] = Field(None, description="错误详情")


class CameraSnapshot(BaseModel):
    """摄像头快照模式"""
    camera_id: int = Field(..., description="摄像头ID")
    camera_name: str = Field(..., description="摄像头名称")
    snapshot_url: str = Field(..., description="快照URL")
    capture_time: datetime = Field(..., description="抓取时间")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    message: str = Field(..., description="抓取结果消息")


class CameraStatus(BaseModel):
    """摄像头状态模式"""
    camera_id: int = Field(..., description="摄像头ID")
    camera_name: str = Field(..., description="摄像头名称")
    is_active: bool = Field(..., description="是否启用")
    is_online: bool = Field(..., description="是否在线")
    last_heartbeat: Optional[datetime] = Field(None, description="最后心跳时间")
    stream_status: str = Field(..., description="流状态")
    error_message: Optional[str] = Field(None, description="错误消息")
    timestamp: datetime = Field(..., description="状态时间")


class CameraAnalysisConfig(BaseModel):
    """摄像头分析配置模式"""
    camera_id: int = Field(..., description="摄像头ID")
    enable_analysis: bool = Field(True, description="启用分析")
    analysis_interval: int = Field(5, ge=1, le=60, description="分析间隔(秒)")
    confidence_threshold: float = Field(0.7, ge=0.0, le=1.0, description="置信度阈值")
    max_fps: int = Field(25, ge=1, le=60, description="最大帧率")
    quality: str = Field("medium", description="图像质量")
    
    @validator('quality')
    def validate_quality(cls, v):
        if v not in ['low', 'medium', 'high']:
            raise ValueError('质量必须是 low, medium 或 high')
        return v 