"""
服务层模块 - 智能视频监控预警系统
提供完整的业务逻辑和服务功能
"""

# AI分析服务
from .ai import AIAnalyzer

# 预警服务
from .alert import AlertService

# 摄像头服务
from .camera import (
    CameraConnector,
    CameraConnection,
    CameraStatusMonitor,
    CameraManager
)

# 配置服务
from .config import (
    ConfigService,
    ConfigurationManager,
    ConfigChangeType,
    ConfigChangeEvent
)

# 通知服务
from .notification import WebSocketService

# 存储服务
from .storage import FileStorageService

# 视频处理服务
from .video import (
    VideoManager,
    FrameBufferManager,
    VideoPipeline,
    MotionDetectionManager,
    PreFilterProcessor,
    FrameAggregator,
    AIIntegrationService
)

__all__ = [
    # AI分析服务
    "AIAnalyzer",
    
    # 预警服务
    "AlertService",
    
    # 摄像头服务
    "CameraConnector",
    "CameraConnection", 
    "CameraStatusMonitor",
    "CameraManager",
    
    # 配置服务
    "ConfigService",
    "ConfigurationManager",
    "ConfigChangeType",
    "ConfigChangeEvent",
    
    # 通知服务
    "WebSocketService",
    
    # 存储服务
    "FileStorageService",
    
    # 视频处理服务
    "VideoManager",
    "FrameBufferManager",
    "VideoPipeline",
    "MotionDetectionManager",
    "PreFilterProcessor",
    "FrameAggregator",
    "AIIntegrationService"
] 