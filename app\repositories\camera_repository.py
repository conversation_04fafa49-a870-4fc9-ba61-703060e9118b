"""
摄像头数据访问层
提供摄像头配置驱动的数据访问接口
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..database.base_repository import BaseRepository
from ..models.camera import Camera
from ..models.analysis_rule import AnalysisRule
from ..models.rule_behavior_mapping import RuleBehaviorMapping
from ..models.anomaly_behavior import AnomalyBehavior
from ..core.logger import logger


class CameraRepository(BaseRepository[Camera]):
    """摄像头Repository - 配置驱动架构核心"""
    
    def __init__(self, db: Session):
        super().__init__(db, Camera)
    
    async def get_active_cameras(self) -> List[Camera]:
        """获取所有在线的启用AI分析的摄像头"""
        return await self.find_by_conditions([
            Camera.status == "online",
            Camera.analysis_enabled == True,
            Camera.deleted == False
        ])
    
    async def get_camera_by_code(self, code: str) -> Optional[Camera]:
        """根据设备编号获取摄像头"""
        return await self.find_by_field("code", code)
    
    async def get_cameras_by_store(self, store_id: int) -> List[Camera]:
        """获取指定门店的所有摄像头"""
        return await self.find_by_field("store_id", store_id)
    
    async def get_camera_analysis_config(self, camera_id: int) -> Dict[str, Any]:
        """
        获取摄像头的完整AI分析配置
        这是配置驱动架构的核心方法
        """
        try:
            # 获取摄像头信息
            camera = await self.get_by_id(camera_id)
            if not camera:
                return {}
            
            # 获取摄像头关联的分析规则
            rules_query = self.db.query(AnalysisRule).filter(
                and_(
                    AnalysisRule.camera_id == camera_id,
                    AnalysisRule.is_enabled == True,
                    AnalysisRule.deleted == False
                )
            ).order_by(AnalysisRule.priority.desc())
            
            rules = rules_query.all()
            
            config = {
                "camera_id": camera_id,
                "camera_code": camera.code,
                "camera_name": camera.name,
                "store_id": camera.store_id,
                "location": camera.location,
                "analysis_fps": float(camera.analysis_fps or 1.0),
                "analysis_scenarios": camera.analysis_scenarios or [],
                "rules": []
            }
            
            # 构建每个规则的配置
            for rule in rules:
                rule_config = {
                    "rule_id": rule.id,
                    "rule_name": rule.name,
                    "rule_type": rule.rule_type,
                    "confidence_threshold": float(rule.confidence_threshold),
                    "consecutive_frames": rule.consecutive_frames,
                    "time_window_seconds": rule.time_window_seconds,
                    "cooldown_seconds": rule.cooldown_seconds,
                    "severity_override": rule.severity_override,
                    "trigger_conditions": rule.trigger_conditions or {},
                    "behaviors": []
                }
                
                # 获取规则关联的异常行为
                mappings_query = self.db.query(RuleBehaviorMapping, AnomalyBehavior).join(
                    AnomalyBehavior, RuleBehaviorMapping.behavior_id == AnomalyBehavior.id
                ).filter(
                    and_(
                        RuleBehaviorMapping.rule_id == rule.id,
                        RuleBehaviorMapping.is_enabled == True,
                        RuleBehaviorMapping.deleted == False,
                        AnomalyBehavior.is_enabled == True,
                        AnomalyBehavior.deleted == False
                    )
                )
                
                mappings = mappings_query.all()
                
                for mapping, behavior in mappings:
                    behavior_config = {
                        "behavior_id": behavior.id,
                        "behavior_code": behavior.code,
                        "behavior_name": behavior.name,
                        "category": behavior.category,
                        "description": behavior.description,
                        "ai_keywords": behavior.ai_keywords or [],
                        "default_severity": behavior.default_severity,
                        "confidence_threshold": float(mapping.confidence_threshold_override or behavior.default_confidence_threshold),
                        "severity": mapping.severity_override or behavior.default_severity,
                        "weight": float(mapping.weight),
                        "custom_params": mapping.custom_params or {}
                    }
                    rule_config["behaviors"].append(behavior_config)
                
                config["rules"].append(rule_config)
            
            logger.info(f"获取摄像头 {camera_id} 分析配置，共 {len(config['rules'])} 个规则")
            return config
            
        except Exception as e:
            logger.error(f"获取摄像头 {camera_id} 分析配置失败: {e}")
            return {}
    
    async def get_camera_enabled_behaviors(self, camera_id: int) -> List[Dict[str, Any]]:
        """获取摄像头启用的所有异常行为"""
        try:
            query = self.db.query(AnomalyBehavior).join(
                RuleBehaviorMapping, AnomalyBehavior.id == RuleBehaviorMapping.behavior_id
            ).join(
                AnalysisRule, RuleBehaviorMapping.rule_id == AnalysisRule.id
            ).filter(
                and_(
                    AnalysisRule.camera_id == camera_id,
                    AnalysisRule.is_enabled == True,
                    RuleBehaviorMapping.is_enabled == True,
                    AnomalyBehavior.is_enabled == True,
                    AnalysisRule.deleted == False,
                    RuleBehaviorMapping.deleted == False,
                    AnomalyBehavior.deleted == False
                )
            ).distinct()
            
            behaviors = query.all()
            
            return [{
                "id": behavior.id,
                "code": behavior.code,
                "name": behavior.name,
                "category": behavior.category,
                "description": behavior.description,
                "ai_keywords": behavior.ai_keywords or [],
                "default_severity": behavior.default_severity
            } for behavior in behaviors]
            
        except Exception as e:
            logger.error(f"获取摄像头 {camera_id} 启用行为失败: {e}")
            return []
    
    async def update_camera_status(self, camera_id: int, status: str, 
                                 last_heartbeat: Optional[str] = None) -> bool:
        """更新摄像头状态"""
        try:
            update_data = {"status": status}
            if last_heartbeat:
                update_data["last_heartbeat_at"] = last_heartbeat
            
            success = await self.update_by_id(camera_id, update_data)
            if success:
                logger.info(f"摄像头 {camera_id} 状态更新为: {status}")
            return success
            
        except Exception as e:
            logger.error(f"更新摄像头 {camera_id} 状态失败: {e}")
            return False
    
    async def get_store_cameras_summary(self, store_id: int) -> Dict[str, Any]:
        """获取门店摄像头状态汇总"""
        try:
            cameras = await self.get_cameras_by_store(store_id)
            
            summary = {
                "total_count": len(cameras),
                "online_count": 0,
                "offline_count": 0,
                "error_count": 0,
                "analysis_enabled_count": 0,
                "status_breakdown": {}
            }
            
            for camera in cameras:
                # 状态统计
                if camera.status == "online":
                    summary["online_count"] += 1
                elif camera.status == "offline":
                    summary["offline_count"] += 1
                elif camera.status == "error":
                    summary["error_count"] += 1
                
                # AI分析启用统计
                if camera.analysis_enabled:
                    summary["analysis_enabled_count"] += 1
                
                # 详细状态分布
                status = camera.status
                if status not in summary["status_breakdown"]:
                    summary["status_breakdown"][status] = 0
                summary["status_breakdown"][status] += 1
            
            return summary
            
        except Exception as e:
            logger.error(f"获取门店 {store_id} 摄像头汇总失败: {e}")
            return {}
    
    async def batch_update_camera_heartbeat(self, camera_heartbeats: List[Dict[str, Any]]) -> int:
        """批量更新摄像头心跳时间"""
        try:
            updated_count = 0
            for heartbeat in camera_heartbeats:
                camera_id = heartbeat.get("camera_id")
                timestamp = heartbeat.get("timestamp")
                status = heartbeat.get("status", "online")
                
                if camera_id and timestamp:
                    success = await self.update_camera_status(camera_id, status, timestamp)
                    if success:
                        updated_count += 1
            
            logger.info(f"批量更新摄像头心跳，成功更新 {updated_count} 个摄像头")
            return updated_count
            
        except Exception as e:
            logger.error(f"批量更新摄像头心跳失败: {e}")
            return 0 