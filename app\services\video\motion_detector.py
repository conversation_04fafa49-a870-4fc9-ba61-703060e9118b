"""
运动检测模块

主要功能：
1. 基于OpenCV的背景减除算法
2. 运动区域检测和分析
3. 自适应阈值调整
4. 运动分数计算
5. 预过滤机制（三层优化的第一层）
"""

import cv2
import numpy as np
import logging
import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from threading import Lock
import asyncio

from .frame_buffer import VideoFrame

logger = logging.getLogger(__name__)


class MotionDetectionAlgorithm(Enum):
    """运动检测算法类型"""
    MOG2 = "MOG2"  # 混合高斯背景减除
    KNN = "KNN"    # K近邻背景减除
    GMG = "GMG"    # 几何多模态背景减除
    FRAME_DIFF = "FRAME_DIFF"  # 帧差法


@dataclass
class MotionDetectionConfig:
    """运动检测配置"""
    algorithm: MotionDetectionAlgorithm = MotionDetectionAlgorithm.MOG2
    
    # 通用参数
    detection_area_ratio: float = 0.05  # 触发检测的最小运动区域比例
    motion_threshold: float = 0.3  # 运动分数阈值
    min_contour_area: int = 500  # 最小轮廓面积
    max_contour_area: int = 50000  # 最大轮廓面积
    
    # MOG2参数
    mog2_detect_shadows: bool = True
    mog2_history: int = 500
    mog2_var_threshold: float = 16.0
    
    # KNN参数
    knn_detect_shadows: bool = True
    knn_history: int = 500
    knn_dist2_threshold: float = 400.0
    
    # 帧差法参数
    frame_diff_threshold: int = 30
    frame_diff_blur_kernel: int = 21
    
    # 自适应参数
    enable_adaptive_threshold: bool = True
    threshold_update_rate: float = 0.1
    max_threshold_adjustment: float = 0.5


@dataclass
class MotionDetectionResult:
    """运动检测结果"""
    motion_score: float  # 运动分数 (0.0-1.0)
    has_significant_motion: bool  # 是否有显著运动
    motion_areas: List[Tuple[int, int, int, int]]  # 运动区域 (x, y, w, h)
    total_motion_area: int  # 总运动面积
    frame_area: int  # 帧总面积
    motion_ratio: float  # 运动面积比例
    detection_time_ms: float  # 检测耗时（毫秒）
    algorithm_used: str  # 使用的算法
    metadata: Dict[str, Any] = field(default_factory=dict)


class MotionDetector:
    """单个视频源的运动检测器"""
    
    def __init__(self, source_id: str, config: MotionDetectionConfig):
        self.source_id = source_id
        self.config = config
        
        # 背景减除器
        self._background_subtractor = None
        self._init_background_subtractor()
        
        # 帧差法相关
        self._previous_frame = None
        self._frame_history = []
        
        # 自适应阈值
        self._adaptive_threshold = config.motion_threshold
        self._motion_history = []
        self._last_adaptation_time = time.time()
        
        # 统计信息
        self._detection_count = 0
        self._total_detection_time = 0.0
        self._motion_detected_count = 0
        
        # 线程锁
        self._lock = Lock()
        
        logger.info(f"运动检测器已初始化 - 源: {source_id}, 算法: {config.algorithm.value}")
    
    def _init_background_subtractor(self):
        """初始化背景减除器"""
        if self.config.algorithm == MotionDetectionAlgorithm.MOG2:
            self._background_subtractor = cv2.createBackgroundSubtractorMOG2(
                history=self.config.mog2_history,
                varThreshold=self.config.mog2_var_threshold,
                detectShadows=self.config.mog2_detect_shadows
            )
        elif self.config.algorithm == MotionDetectionAlgorithm.KNN:
            self._background_subtractor = cv2.createBackgroundSubtractorKNN(
                history=self.config.knn_history,
                dist2Threshold=self.config.knn_dist2_threshold,
                detectShadows=self.config.knn_detect_shadows
            )
        elif self.config.algorithm == MotionDetectionAlgorithm.GMG:
            # OpenCV 4.x 中可能不支持 GMG，使用 MOG2 替代
            logger.warning("GMG算法不可用，使用MOG2替代")
            self._background_subtractor = cv2.createBackgroundSubtractorMOG2()
    
    def detect_motion(self, frame: VideoFrame) -> MotionDetectionResult:
        """检测帧中的运动"""
        start_time = time.time()
        
        with self._lock:
            try:
                if self.config.algorithm == MotionDetectionAlgorithm.FRAME_DIFF:
                    result = self._detect_motion_frame_diff(frame.frame_data)
                else:
                    result = self._detect_motion_background_subtraction(frame.frame_data)
                
                # 更新统计信息
                detection_time_ms = (time.time() - start_time) * 1000
                result.detection_time_ms = detection_time_ms
                result.algorithm_used = self.config.algorithm.value
                
                self._detection_count += 1
                self._total_detection_time += detection_time_ms
                
                if result.has_significant_motion:
                    self._motion_detected_count += 1
                
                # 自适应阈值调整
                if self.config.enable_adaptive_threshold:
                    self._update_adaptive_threshold(result.motion_score)
                
                return result
                
            except Exception as e:
                logger.error(f"运动检测失败 - 源: {self.source_id}, 错误: {e}")
                # 返回默认结果
                return MotionDetectionResult(
                    motion_score=0.0,
                    has_significant_motion=False,
                    motion_areas=[],
                    total_motion_area=0,
                    frame_area=frame.frame_data.shape[0] * frame.frame_data.shape[1],
                    motion_ratio=0.0,
                    detection_time_ms=(time.time() - start_time) * 1000,
                    algorithm_used=self.config.algorithm.value,
                    metadata={"error": str(e)}
                )
    
    def _detect_motion_background_subtraction(self, frame: np.ndarray) -> MotionDetectionResult:
        """使用背景减除检测运动"""
        # 预处理帧
        gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        
        # 高斯模糊减少噪声
        blurred_frame = cv2.GaussianBlur(gray_frame, (5, 5), 0)
        
        # 背景减除
        fg_mask = self._background_subtractor.apply(blurred_frame)
        
        # 形态学操作清理噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, kernel)
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 分析运动区域
        return self._analyze_motion_contours(contours, frame.shape)
    
    def _detect_motion_frame_diff(self, frame: np.ndarray) -> MotionDetectionResult:
        """使用帧差法检测运动"""
        # 预处理帧
        gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        blurred_frame = cv2.GaussianBlur(gray_frame, (self.config.frame_diff_blur_kernel, self.config.frame_diff_blur_kernel), 0)
        
        if self._previous_frame is None:
            self._previous_frame = blurred_frame
            return MotionDetectionResult(
                motion_score=0.0,
                has_significant_motion=False,
                motion_areas=[],
                total_motion_area=0,
                frame_area=frame.shape[0] * frame.shape[1],
                motion_ratio=0.0,
                detection_time_ms=0.0,
                algorithm_used=self.config.algorithm.value
            )
        
        # 计算帧差
        frame_diff = cv2.absdiff(self._previous_frame, blurred_frame)
        
        # 二值化
        _, thresh = cv2.threshold(frame_diff, self.config.frame_diff_threshold, 255, cv2.THRESH_BINARY)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 更新前一帧
        self._previous_frame = blurred_frame
        
        # 分析运动区域
        return self._analyze_motion_contours(contours, frame.shape)
    
    def _analyze_motion_contours(self, contours: List, frame_shape: Tuple) -> MotionDetectionResult:
        """分析运动轮廓"""
        frame_area = frame_shape[0] * frame_shape[1]
        motion_areas = []
        total_motion_area = 0
        
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # 过滤太小或太大的轮廓
            if self.config.min_contour_area <= area <= self.config.max_contour_area:
                x, y, w, h = cv2.boundingRect(contour)
                motion_areas.append((x, y, w, h))
                total_motion_area += area
        
        # 计算运动比例和分数
        motion_ratio = total_motion_area / frame_area
        motion_score = min(motion_ratio / self.config.detection_area_ratio, 1.0)
        
        # 判断是否有显著运动
        threshold = self._adaptive_threshold if self.config.enable_adaptive_threshold else self.config.motion_threshold
        has_significant_motion = motion_score >= threshold and motion_ratio >= self.config.detection_area_ratio
        
        return MotionDetectionResult(
            motion_score=motion_score,
            has_significant_motion=has_significant_motion,
            motion_areas=motion_areas,
            total_motion_area=total_motion_area,
            frame_area=frame_area,
            motion_ratio=motion_ratio,
            detection_time_ms=0.0,  # 将在调用方设置
            algorithm_used=self.config.algorithm.value,
            metadata={
                "contour_count": len(contours),
                "filtered_contour_count": len(motion_areas),
                "adaptive_threshold": self._adaptive_threshold
            }
        )
    
    def _update_adaptive_threshold(self, motion_score: float):
        """更新自适应阈值"""
        current_time = time.time()
        
        # 记录运动分数历史
        self._motion_history.append(motion_score)
        if len(self._motion_history) > 100:  # 保持最近100个分数
            self._motion_history.pop(0)
        
        # 每5秒更新一次阈值
        if current_time - self._last_adaptation_time > 5.0 and len(self._motion_history) >= 10:
            # 计算历史运动分数的统计信息
            mean_score = np.mean(self._motion_history)
            std_score = np.std(self._motion_history)
            
            # 根据统计信息调整阈值
            if std_score > 0.1:  # 运动变化较大，降低阈值
                adjustment = -self.config.threshold_update_rate * std_score
            else:  # 运动变化较小，可能需要提高阈值
                adjustment = self.config.threshold_update_rate * (0.1 - std_score)
            
            # 限制调整幅度
            adjustment = np.clip(adjustment, -self.config.max_threshold_adjustment, self.config.max_threshold_adjustment)
            
            # 更新阈值
            new_threshold = self._adaptive_threshold + adjustment
            self._adaptive_threshold = np.clip(new_threshold, 0.1, 0.9)
            
            self._last_adaptation_time = current_time
            
            logger.debug(f"自适应阈值更新 - 源: {self.source_id}, 新阈值: {self._adaptive_threshold:.3f}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        with self._lock:
            avg_detection_time = self._total_detection_time / max(self._detection_count, 1)
            motion_detection_rate = self._motion_detected_count / max(self._detection_count, 1)
            
            return {
                "source_id": self.source_id,
                "algorithm": self.config.algorithm.value,
                "detection_count": self._detection_count,
                "motion_detected_count": self._motion_detected_count,
                "motion_detection_rate": motion_detection_rate,
                "avg_detection_time_ms": avg_detection_time,
                "adaptive_threshold": self._adaptive_threshold,
                "motion_history_length": len(self._motion_history)
            }
    
    def reset(self):
        """重置检测器状态"""
        with self._lock:
            self._init_background_subtractor()
            self._previous_frame = None
            self._frame_history.clear()
            self._motion_history.clear()
            self._adaptive_threshold = self.config.motion_threshold
            logger.info(f"运动检测器已重置 - 源: {self.source_id}")


class MotionDetectionManager:
    """运动检测管理器
    
    管理多个视频源的运动检测器
    """
    
    def __init__(self, default_config: Optional[MotionDetectionConfig] = None):
        self.default_config = default_config or MotionDetectionConfig()
        self._detectors: Dict[str, MotionDetector] = {}
        self._configs: Dict[str, MotionDetectionConfig] = {}
        self._lock = Lock()
        
        logger.info("运动检测管理器已初始化")
    
    def add_detector(self, source_id: str, config: Optional[MotionDetectionConfig] = None) -> MotionDetector:
        """添加运动检测器"""
        with self._lock:
            if source_id in self._detectors:
                logger.warning(f"运动检测器已存在，将重新创建 - 源: {source_id}")
            
            config = config or self.default_config
            detector = MotionDetector(source_id, config)
            
            self._detectors[source_id] = detector
            self._configs[source_id] = config
            
            logger.info(f"运动检测器已添加 - 源: {source_id}")
            return detector
    
    def remove_detector(self, source_id: str):
        """移除运动检测器"""
        with self._lock:
            if source_id in self._detectors:
                del self._detectors[source_id]
                del self._configs[source_id]
                logger.info(f"运动检测器已移除 - 源: {source_id}")
    
    def get_detector(self, source_id: str) -> Optional[MotionDetector]:
        """获取运动检测器"""
        with self._lock:
            return self._detectors.get(source_id)
    
    def detect_motion(self, source_id: str, frame: VideoFrame) -> Optional[MotionDetectionResult]:
        """检测运动"""
        detector = self.get_detector(source_id)
        if detector:
            return detector.detect_motion(frame)
        else:
            logger.warning(f"未找到运动检测器 - 源: {source_id}")
            return None
    
    def get_all_statistics(self) -> Dict[str, Dict[str, Any]]:
        """获取所有检测器的统计信息"""
        with self._lock:
            return {
                source_id: detector.get_statistics()
                for source_id, detector in self._detectors.items()
            }
    
    def reset_detector(self, source_id: str):
        """重置指定检测器"""
        detector = self.get_detector(source_id)
        if detector:
            detector.reset()
    
    def reset_all_detectors(self):
        """重置所有检测器"""
        with self._lock:
            for detector in self._detectors.values():
                detector.reset()
            logger.info("所有运动检测器已重置")
    
    def update_config(self, source_id: str, config: MotionDetectionConfig):
        """更新检测器配置"""
        with self._lock:
            if source_id in self._detectors:
                # 重新创建检测器应用新配置
                self._detectors[source_id] = MotionDetector(source_id, config)
                self._configs[source_id] = config
                logger.info(f"运动检测器配置已更新 - 源: {source_id}")
    
    def get_detector_count(self) -> int:
        """获取检测器数量"""
        with self._lock:
            return len(self._detectors) 