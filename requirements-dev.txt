# 继承生产环境依赖
-r requirements.txt

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-benchmark==4.0.0
httpx==0.25.2  # 用于测试API

# 代码质量工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pylint==3.0.3

# 开发工具
pre-commit==3.5.0
watchdog==3.0.0
ipython==8.18.0
jupyter==1.0.0

# 性能分析
memory-profiler==0.61.0
py-spy==0.3.14
line-profiler==4.1.1

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.7

# 环境管理
python-dotenv==1.0.0

# 调试工具
pdbpp==0.10.3
rich==13.7.0 