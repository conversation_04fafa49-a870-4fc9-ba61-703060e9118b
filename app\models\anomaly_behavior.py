"""
异常行为数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, 
    DECIMAL, Enum as SQLEnum, JSON
)
from sqlalchemy.orm import relationship
from enum import Enum
from .base import BaseModel


class SeverityLevel(Enum):
    """严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AnomalyBehavior(BaseModel):
    """异常行为模型"""
    
    __tablename__ = "anomaly_behaviors"
    
    behavior_name = Column(String(100), nullable=False, index=True, comment="异常行为名称")
    behavior_code = Column(String(50), unique=True, nullable=False, index=True, comment="行为编码")
    description = Column(Text, comment="行为描述")
    ai_keywords = Column(Text, comment="AI识别关键词")
    severity_level = Column(
        SQLEnum(SeverityLevel), 
        default=SeverityLevel.MEDIUM, 
        nullable=False, 
        comment="严重程度"
    )
    default_confidence_threshold = Column(
        DECIMAL(3, 2), 
        default=0.7, 
        comment="默认置信度阈值"
    )
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 行为特征配置
    detection_config = Column(JSON, comment="检测配置JSON")
    
    # 关联关系
    rule_mappings = relationship("RuleBehaviorMapping", back_populates="anomaly_behavior")
    prompt_templates = relationship("PromptTemplate", back_populates="anomaly_behavior")
    alert_events = relationship("AlertEvent", back_populates="anomaly_behavior")
    
    def __repr__(self) -> str:
        return f"<AnomalyBehavior(id={self.id}, name={self.behavior_name}, code={self.behavior_code})>"
    
    @property
    def is_critical(self) -> bool:
        """是否为关键异常"""
        return self.severity_level == SeverityLevel.CRITICAL
    
    @property
    def keywords_list(self) -> list:
        """获取关键词列表"""
        if not self.ai_keywords:
            return []
        return [kw.strip() for kw in self.ai_keywords.split(",") if kw.strip()]
    
    def get_detection_config(self, key: str, default=None):
        """获取检测配置"""
        if not self.detection_config:
            return default
        return self.detection_config.get(key, default) 