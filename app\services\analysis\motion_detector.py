"""
运动检测器 - 第一层预过滤组件
使用OpenCV实现轻量级运动检测，过滤静态帧
"""

import cv2
import numpy as np
from typing import Optional, Tuple
import time

from ...core.logger import logger


class MotionDetector:
    """运动检测器 - OpenCV背景减法实现"""
    
    def __init__(self, threshold: float = 0.01, min_area: int = 500, 
                 learning_rate: float = 0.01, history_frames: int = 500):
        self.threshold = threshold  # 运动阈值（占图像面积的比例）
        self.min_area = min_area   # 最小运动区域面积
        self.learning_rate = learning_rate  # 背景学习速率
        
        # 创建背景减法器
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
            history=history_frames,
            varThreshold=50,
            detectShadows=True
        )
        
        # 形态学操作核
        self.kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        
        # 统计信息
        self.total_frames = 0
        self.motion_frames = 0
        self.static_frames = 0
        
        # 性能监控
        self.processing_times = []
        self.avg_processing_time = 0
        
        logger.info(f"运动检测器初始化 - 阈值: {threshold}, 最小区域: {min_area}")
    
    def detect_motion(self, frame: np.ndarray) -> bool:
        """
        检测帧中是否有运动
        
        Args:
            frame: 输入视频帧
            
        Returns:
            bool: True表示检测到运动，False表示静态帧
        """
        start_time = time.time()
        
        try:
            self.total_frames += 1
            
            # 预处理：调整大小以提高处理速度
            height, width = frame.shape[:2]
            if width > 640:  # 如果图像过大，缩小以提高速度
                scale = 640 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                frame_resized = cv2.resize(frame, (new_width, new_height))
            else:
                frame_resized = frame
            
            # 背景减法
            fg_mask = self.background_subtractor.apply(
                frame_resized, learningRate=self.learning_rate
            )
            
            # 去噪：形态学操作
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, self.kernel)
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, self.kernel)
            
            # 计算运动区域
            motion_area = cv2.countNonZero(fg_mask)
            total_area = fg_mask.shape[0] * fg_mask.shape[1]
            motion_ratio = motion_area / total_area
            
            # 判断是否有明显运动
            has_motion = motion_ratio > self.threshold
            
            # 额外检查：查找轮廓以验证运动区域
            if has_motion:
                contours, _ = cv2.findContours(
                    fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
                )
                
                # 检查是否有足够大的运动区域
                large_contours = [c for c in contours if cv2.contourArea(c) > self.min_area]
                has_motion = len(large_contours) > 0
            
            # 更新统计
            if has_motion:
                self.motion_frames += 1
            else:
                self.static_frames += 1
            
            # 性能统计
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            if len(self.processing_times) > 100:  # 保留最近100次
                self.processing_times.pop(0)
            self.avg_processing_time = sum(self.processing_times) / len(self.processing_times)
            
            logger.debug(f"运动检测: 运动比例={motion_ratio:.4f}, 结果={'运动' if has_motion else '静态'}")
            
            return has_motion
            
        except Exception as e:
            logger.error(f"运动检测失败: {e}")
            return True  # 出错时不过滤帧
    
    def detect_motion_with_details(self, frame: np.ndarray) -> Tuple[bool, dict]:
        """
        检测运动并返回详细信息
        
        Returns:
            tuple: (是否有运动, 详细信息字典)
        """
        start_time = time.time()
        
        try:
            # 执行运动检测
            has_motion = self.detect_motion(frame)
            
            # 获取前景掩码用于详细分析
            height, width = frame.shape[:2]
            if width > 640:
                scale = 640 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                frame_resized = cv2.resize(frame, (new_width, new_height))
            else:
                frame_resized = frame
            
            fg_mask = self.background_subtractor.apply(frame_resized, learningRate=0)  # 不更新背景
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, self.kernel)
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, self.kernel)
            
            # 分析运动区域
            contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            motion_regions = []
            total_motion_area = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > self.min_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    # 如果图像被缩放了，需要还原坐标
                    if width > 640:
                        x = int(x / scale)
                        y = int(y / scale)
                        w = int(w / scale)
                        h = int(h / scale)
                    
                    motion_regions.append({
                        "bbox": [x, y, w, h],
                        "area": area,
                        "center": [x + w//2, y + h//2]
                    })
                    total_motion_area += area
            
            processing_time = time.time() - start_time
            
            details = {
                "has_motion": has_motion,
                "motion_regions": motion_regions,
                "total_motion_area": total_motion_area,
                "motion_ratio": total_motion_area / (fg_mask.shape[0] * fg_mask.shape[1]),
                "processing_time": processing_time,
                "frame_size": [width, height]
            }
            
            return has_motion, details
            
        except Exception as e:
            logger.error(f"详细运动检测失败: {e}")
            return True, {"error": str(e)}
    
    def reset_background(self):
        """重置背景模型"""
        try:
            # 重新创建背景减法器
            self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
                history=500, varThreshold=50, detectShadows=True
            )
            logger.info("背景模型已重置")
            
        except Exception as e:
            logger.error(f"重置背景模型失败: {e}")
    
    def adjust_sensitivity(self, threshold: float = None, min_area: int = None):
        """调整检测敏感度"""
        if threshold is not None:
            self.threshold = threshold
            logger.info(f"运动阈值调整为: {threshold}")
        
        if min_area is not None:
            self.min_area = min_area
            logger.info(f"最小运动区域调整为: {min_area}")
    
    def get_statistics(self) -> dict:
        """获取检测器统计信息"""
        return {
            "total_frames": self.total_frames,
            "motion_frames": self.motion_frames,
            "static_frames": self.static_frames,
            "motion_rate": round(self.motion_frames / max(self.total_frames, 1) * 100, 2),
            "filter_efficiency": round(self.static_frames / max(self.total_frames, 1) * 100, 2),
            "avg_processing_time": round(self.avg_processing_time * 1000, 2),  # 毫秒
            "threshold": self.threshold,
            "min_area": self.min_area
        }
    
    def get_background_image(self) -> Optional[np.ndarray]:
        """获取当前背景图像"""
        try:
            return self.background_subtractor.getBackgroundImage()
        except Exception as e:
            logger.error(f"获取背景图像失败: {e}")
            return None 