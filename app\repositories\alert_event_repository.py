"""
预警事件数据访问层
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func

from ..database.base_repository import BaseRepository
from ..models.alert_event import AlertEvent
from ..core.logger import logger


class AlertEventRepository(BaseRepository[AlertEvent]):
    """预警事件Repository"""
    
    def __init__(self, db: Session):
        super().__init__(db, AlertEvent)
    
    async def get_alert_by_uuid(self, uuid: str) -> Optional[AlertEvent]:
        """根据UUID获取预警"""
        return await self.find_by_field("uuid", uuid)
    
    async def get_recent_alerts(self, hours: int = 24, limit: int = 100) -> List[AlertEvent]:
        """获取最近的预警事件"""
        since_time = datetime.utcnow() - timedelta(hours=hours)
        return await self.find_by_conditions([
            AlertEvent.trigger_time >= since_time,
            AlertEvent.deleted == False
        ], order_by=[AlertEvent.trigger_time.desc()], limit=limit)
    
    async def get_camera_alerts(self, camera_id: int, hours: int = 24) -> List[AlertEvent]:
        """获取摄像头的预警事件"""
        since_time = datetime.utcnow() - timedelta(hours=hours)
        return await self.find_by_conditions([
            AlertEvent.camera_id == camera_id,
            AlertEvent.trigger_time >= since_time,
            AlertEvent.deleted == False
        ], order_by=[AlertEvent.trigger_time.desc()])
    
    async def get_pending_alerts(self) -> List[AlertEvent]:
        """获取待处理的预警"""
        return await self.find_by_conditions([
            AlertEvent.status == "pending",
            AlertEvent.deleted == False
        ], order_by=[AlertEvent.trigger_time.desc()])
    
    async def create_alert(self, alert_data: Dict[str, Any]) -> Optional[str]:
        """创建预警事件，返回UUID"""
        try:
            import uuid
            alert_uuid = str(uuid.uuid4())
            alert_data["uuid"] = alert_uuid
            
            alert_id = await self.create(alert_data)
            if alert_id:
                logger.info(f"创建预警事件: {alert_uuid}")
                return alert_uuid
            return None
            
        except Exception as e:
            logger.error(f"创建预警事件失败: {e}")
            return None
    
    async def update_alert_status(self, alert_uuid: str, status: str, 
                                processed_by: int = None, notes: str = None) -> bool:
        """更新预警状态"""
        try:
            update_data = {
                "status": status,
                "processed_at": datetime.utcnow()
            }
            if processed_by:
                update_data["processed_by"] = processed_by
            if notes:
                update_data["resolution_notes"] = notes
            
            success = await self.update_by_field("uuid", alert_uuid, update_data)
            if success:
                logger.info(f"预警 {alert_uuid} 状态更新为: {status}")
            return success
            
        except Exception as e:
            logger.error(f"更新预警 {alert_uuid} 状态失败: {e}")
            return False
    
    async def get_alerts_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取预警统计信息"""
        try:
            since_time = datetime.utcnow() - timedelta(days=days)
            alerts = await self.find_by_conditions([
                AlertEvent.trigger_time >= since_time,
                AlertEvent.deleted == False
            ])
            
            stats = {
                "total_count": len(alerts),
                "severity_breakdown": {},
                "status_breakdown": {},
                "behavior_breakdown": {},
                "daily_trend": {}
            }
            
            for alert in alerts:
                # 严重程度统计
                severity = alert.severity
                if severity not in stats["severity_breakdown"]:
                    stats["severity_breakdown"][severity] = 0
                stats["severity_breakdown"][severity] += 1
                
                # 状态统计
                status = alert.status
                if status not in stats["status_breakdown"]:
                    stats["status_breakdown"][status] = 0
                stats["status_breakdown"][status] += 1
                
                # 每日趋势
                date_key = alert.trigger_time.strftime("%Y-%m-%d")
                if date_key not in stats["daily_trend"]:
                    stats["daily_trend"][date_key] = 0
                stats["daily_trend"][date_key] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"获取预警统计失败: {e}")
            return {} 