"""
智能视频监控预警系统 - 统一启动脚本
支持一键配置和直接启动两种模式
"""
import os
import sys
import subprocess
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('app.log', encoding='utf-8')
        ]
    )


def run_command(command, description, check_error=True):
    """执行命令并显示进度"""
    print(f"🔄 {description}...")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        
        if check_error and result.returncode != 0:
            print(f"❌ {description}失败:")
            print(f"   错误信息: {result.stderr}")
            return False
        else:
            print(f"✅ {description}完成")
            return True
            
    except Exception as e:
        print(f"❌ {description}异常: {e}")
        return False


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.9+")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_env_file():
    """检查或创建环境配置文件"""
    print("⚙️  检查环境配置...")
    
    env_file = Path(".env")
    env_example = Path("env.example")
    
    if not env_example.exists():
        print("❌ 缺少env.example文件")
        return False
    
    if not env_file.exists():
        print("📝 创建.env配置文件...")
        try:
            import shutil
            shutil.copy2(env_example, env_file)
            print("✅ 已从env.example创建.env文件")
            print("⚠️  请修改.env文件中的配置参数:")
            print("   - 数据库连接信息")
            print("   - Redis连接信息") 
            print("   - MinIO存储配置")
            print("   - 通义千问API密钥")
            
            response = input("\n是否已配置完成? (y/n): ")
            if response.lower() != 'y':
                print("❌ 请先配置.env文件再运行")
                return False
                
        except Exception as e:
            print(f"❌ 创建.env文件失败: {e}")
            return False
    
    print("✅ 环境配置文件检查通过")
    return True


def install_dependencies():
    """安装Python依赖"""
    print("📦 检查并安装依赖包...")
    
    if not Path("requirements.txt").exists():
        print("❌ 缺少requirements.txt文件")
        return False
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if not in_venv:
        print("💡 建议在虚拟环境中安装依赖")
        print("   创建虚拟环境命令:")
        print("   python -m venv venv")
        print("   # Linux/macOS: source venv/bin/activate")
        print("   # Windows: venv\\Scripts\\activate")
        
        response = input("\n是否继续在当前环境安装? (y/n): ")
        if response.lower() != 'y':
            print("❌ 用户取消安装")
            return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "安装Python依赖包"
    )


def check_database_config():
    """检查数据库配置"""
    print("💾 检查数据库配置...")
    
    try:
        env_file = Path(".env")
        if not env_file.exists():
            print("❌ 环境配置文件.env不存在")
            return False
            
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_configs = ['DATABASE_URL', 'REDIS_URL']
        missing_configs = []
        
        for config in required_configs:
            if config not in content or f"{config}=" not in content:
                missing_configs.append(config)
        
        if missing_configs:
            print(f"❌ 缺少必要的数据库配置: {', '.join(missing_configs)}")
            print("   请在.env文件中配置这些参数")
            return False
        
        print("✅ 数据库配置检查通过")
        print("💡 提示: 请确保数据库已手动创建并包含必要的表结构")
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库配置失败: {e}")
        return False


def start_service():
    """启动服务"""
    print("🚀 启动智能视频监控预警系统...")
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("=" * 60)
    print("🎉 系统即将启动!")
    print("📊 Web界面: http://localhost:8000/docs")
    print("🔌 WebSocket: ws://localhost:8000/ws")
    print("📝 日志文件: app.log")
    print("=" * 60)
    print("按Ctrl+C停止服务\n")
    
    try:
        import uvicorn
        from app.main import app
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("👋 收到停止信号，系统已关闭")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)


def show_usage():
    """显示使用说明"""
    print("🎯 智能视频监控预警系统启动脚本")
    print("=" * 50)
    print("使用方法:")
    print("  python start.py          # 直接启动（推荐）")
    print("  python start.py --setup  # 完整配置+启动（首次使用）")
    print("  python start.py --help   # 显示帮助")
    print("=" * 50)


def main():
    """主函数"""
    args = sys.argv[1:] if len(sys.argv) > 1 else []
    
    # 显示帮助
    if "--help" in args or "-h" in args:
        show_usage()
        return
    
    # 完整配置模式（首次使用）
    if "--setup" in args:
        print("🏗️  智能视频监控预警系统 - 完整配置模式")
        print("=" * 60)
        
        # 检查Python版本
        if not check_python_version():
            return
        
        # 检查环境配置
        if not check_env_file():
            return
        
        # 安装依赖
        if not install_dependencies():
            return
        
        # 检查数据库配置
        if not check_database_config():
            return
        
        print("\n" + "=" * 60)
        start_service()
        
    else:
        # 快速启动模式（默认）
        print("🚀 智能视频监控预警系统 - 快速启动")
        print("=" * 60)
        
        # 基本检查
        env_file = Path(".env")
        if not env_file.exists():
            print("⚠️  未找到.env文件，建议运行完整配置:")
            print("   python start.py --setup")
            print("\n如需继续，请先配置:")
            print("   cp env.example .env")
            print("   # 然后编辑.env文件配置数据库等参数")
            return
        
        print("✅ 环境配置文件存在")
        start_service()


if __name__ == "__main__":
    main() 