"""
基础数据访问仓储类
"""

from typing import Generic, TypeVar, Type, List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import IntegrityError

from app.models.base import BaseModel
from app.core.logger import logger
from app.core.exceptions import DatabaseError, RecordNotFoundError

# 泛型类型变量
ModelType = TypeVar("ModelType", bound=BaseModel)


class BaseRepository(Generic[ModelType]):
    """基础仓储类"""
    
    def __init__(self, model: Type[ModelType], session: AsyncSession):
        self.model = model
        self.session = session
    
    async def get_by_id(self, id: int, load_relations: List[str] = None) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        try:
            query = select(self.model).where(self.model.id == id)
            
            # 预加载关联关系
            if load_relations:
                for relation in load_relations:
                    query = query.options(selectinload(getattr(self.model, relation)))
            
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取记录失败 (ID: {id}): {e}")
            raise DatabaseError(f"获取记录失败: {e}")
    
    async def get_by_field(self, field_name: str, value: Any, 
                          load_relations: List[str] = None) -> Optional[ModelType]:
        """根据字段获取单个记录"""
        try:
            query = select(self.model).where(getattr(self.model, field_name) == value)
            
            if load_relations:
                for relation in load_relations:
                    query = query.options(selectinload(getattr(self.model, relation)))
            
            result = await self.session.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"根据字段获取记录失败 ({field_name}={value}): {e}")
            raise DatabaseError(f"获取记录失败: {e}")
    
    async def get_all(self, skip: int = 0, limit: int = 100, 
                     load_relations: List[str] = None) -> List[ModelType]:
        """获取所有记录"""
        try:
            query = select(self.model).offset(skip).limit(limit)
            
            if load_relations:
                for relation in load_relations:
                    query = query.options(selectinload(getattr(self.model, relation)))
            
            result = await self.session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"获取所有记录失败: {e}")
            raise DatabaseError(f"获取记录失败: {e}")
    
    async def get_by_filters(self, filters: Dict[str, Any], 
                            skip: int = 0, limit: int = 100,
                            load_relations: List[str] = None) -> List[ModelType]:
        """根据过滤条件获取记录"""
        try:
            query = select(self.model)
            
            # 应用过滤条件
            for field_name, value in filters.items():
                if hasattr(self.model, field_name):
                    query = query.where(getattr(self.model, field_name) == value)
            
            query = query.offset(skip).limit(limit)
            
            if load_relations:
                for relation in load_relations:
                    query = query.options(selectinload(getattr(self.model, relation)))
            
            result = await self.session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"根据过滤条件获取记录失败: {e}")
            raise DatabaseError(f"获取记录失败: {e}")
    
    async def count(self, filters: Dict[str, Any] = None) -> int:
        """统计记录数量"""
        try:
            query = select(func.count(self.model.id))
            
            if filters:
                for field_name, value in filters.items():
                    if hasattr(self.model, field_name):
                        query = query.where(getattr(self.model, field_name) == value)
            
            result = await self.session.execute(query)
            return result.scalar()
            
        except Exception as e:
            logger.error(f"统计记录数量失败: {e}")
            raise DatabaseError(f"统计失败: {e}")
    
    async def create(self, **kwargs) -> ModelType:
        """创建新记录"""
        try:
            instance = self.model(**kwargs)
            self.session.add(instance)
            await self.session.flush()  # 刷新以获取ID
            await self.session.refresh(instance)  # 刷新实例以获取所有字段
            return instance
            
        except IntegrityError as e:
            await self.session.rollback()
            logger.error(f"创建记录失败，数据完整性错误: {e}")
            raise DatabaseError(f"数据完整性错误: {e}")
        except Exception as e:
            await self.session.rollback()
            logger.error(f"创建记录失败: {e}")
            raise DatabaseError(f"创建失败: {e}")
    
    async def create_from_dict(self, data: Dict[str, Any]) -> ModelType:
        """从字典创建记录"""
        return await self.create(**data)
    
    async def update(self, id: int, **kwargs) -> Optional[ModelType]:
        """更新记录"""
        try:
            # 先获取记录
            instance = await self.get_by_id(id)
            if not instance:
                raise RecordNotFoundError(f"{self.model.__name__} ID {id} 不存在")
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(instance, key):
                    setattr(instance, key, value)
            
            await self.session.flush()
            await self.session.refresh(instance)
            return instance
            
        except RecordNotFoundError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(f"更新记录失败 (ID: {id}): {e}")
            raise DatabaseError(f"更新失败: {e}")
    
    async def update_from_dict(self, id: int, data: Dict[str, Any]) -> Optional[ModelType]:
        """从字典更新记录"""
        return await self.update(id, **data)
    
    async def bulk_update(self, filters: Dict[str, Any], updates: Dict[str, Any]) -> int:
        """批量更新记录"""
        try:
            query = update(self.model)
            
            # 应用过滤条件
            for field_name, value in filters.items():
                if hasattr(self.model, field_name):
                    query = query.where(getattr(self.model, field_name) == value)
            
            # 应用更新
            query = query.values(**updates)
            
            result = await self.session.execute(query)
            return result.rowcount
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"批量更新记录失败: {e}")
            raise DatabaseError(f"批量更新失败: {e}")
    
    async def delete(self, id: int) -> bool:
        """删除记录"""
        try:
            instance = await self.get_by_id(id)
            if not instance:
                raise RecordNotFoundError(f"{self.model.__name__} ID {id} 不存在")
            
            await self.session.delete(instance)
            await self.session.flush()
            return True
            
        except RecordNotFoundError:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.error(f"删除记录失败 (ID: {id}): {e}")
            raise DatabaseError(f"删除失败: {e}")
    
    async def bulk_delete(self, filters: Dict[str, Any]) -> int:
        """批量删除记录"""
        try:
            query = delete(self.model)
            
            # 应用过滤条件
            for field_name, value in filters.items():
                if hasattr(self.model, field_name):
                    query = query.where(getattr(self.model, field_name) == value)
            
            result = await self.session.execute(query)
            return result.rowcount
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"批量删除记录失败: {e}")
            raise DatabaseError(f"批量删除失败: {e}")
    
    async def exists(self, **kwargs) -> bool:
        """检查记录是否存在"""
        try:
            query = select(func.count(self.model.id))
            
            for field_name, value in kwargs.items():
                if hasattr(self.model, field_name):
                    query = query.where(getattr(self.model, field_name) == value)
            
            result = await self.session.execute(query)
            count = result.scalar()
            return count > 0
            
        except Exception as e:
            logger.error(f"检查记录存在性失败: {e}")
            raise DatabaseError(f"检查失败: {e}")
    
    async def get_or_create(self, defaults: Dict[str, Any] = None, **kwargs) -> tuple[ModelType, bool]:
        """获取或创建记录"""
        try:
            # 尝试获取现有记录
            instance = await self.get_by_filters(kwargs, limit=1)
            if instance:
                return instance[0], False
            
            # 创建新记录
            create_data = kwargs.copy()
            if defaults:
                create_data.update(defaults)
            
            new_instance = await self.create(**create_data)
            return new_instance, True
            
        except Exception as e:
            logger.error(f"获取或创建记录失败: {e}")
            raise DatabaseError(f"获取或创建失败: {e}") 