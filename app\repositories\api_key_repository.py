"""
API密钥管理数据访问层
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..database.base_repository import BaseRepository
from ..models.api_key import Api<PERSON><PERSON>
from ..core.logger import logger


class ApiKeyRepository(BaseRepository[ApiKey]):
    """API密钥Repository"""
    
    def __init__(self, db: Session):
        super().__init__(db, ApiKey)
    
    async def get_active_key_by_service(self, service_type: str, provider: str) -> Optional[ApiKey]:
        """获取指定服务的活跃密钥"""
        return await self.find_by_conditions([
            ApiKey.service_type == service_type,
            ApiKey.provider == provider,
            ApiKey.is_active == True,
            ApiKey.deleted == False
        ], single=True)
    
    async def get_available_keys(self, service_type: str) -> List[ApiKey]:
        """获取可用的API密钥"""
        now = datetime.utcnow()
        return await self.find_by_conditions([
            ApiKey.service_type == service_type,
            ApiKey.is_active == True,
            and_(
                or_(ApiKey.expires_at.is_(None), ApiKey.expires_at > now)
            ),
            ApiKey.deleted == False
        ])
    
    async def update_usage(self, key_id: int, quota_used: int) -> bool:
        """更新密钥使用量"""
        try:
            update_data = {
                "quota_used": quota_used,
                "last_used_at": datetime.utcnow()
            }
            success = await self.update_by_id(key_id, update_data)
            if success:
                logger.debug(f"API密钥 {key_id} 使用量更新为: {quota_used}")
            return success
            
        except Exception as e:
            logger.error(f"更新API密钥 {key_id} 使用量失败: {e}")
            return False 