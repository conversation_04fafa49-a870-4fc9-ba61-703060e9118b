"""
SQLAlchemy基础模型
"""
from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, DateTime, Boolean, String, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import AsyncAttrs
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column

class Base(AsyncAttrs, DeclarativeBase):
    """SQLAlchemy基础模型类"""
    pass

class BaseModel(Base):
    """带有公共字段的基础模型"""
    __abstract__ = True
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.now,
        server_default=text("CURRENT_TIMESTAMP"),
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.now,
        onupdate=datetime.now,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间"
    )
    is_deleted: Mapped[bool] = mapped_column(
        <PERSON><PERSON><PERSON>, 
        default=False, 
        server_default=text("0"),
        comment="软删除标记"
    )
    
    def to_dict(self, exclude_fields: Optional[list] = None) -> Dict[str, Any]:
        """转换为字典"""
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                else:
                    result[column.name] = value
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseModel':
        """从字典创建实例"""
        return cls(**data)
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id})>" 