"""
帧聚合器 - 第二层聚合组件
时间窗口内的关键帧选择和批量处理
"""

import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from collections import deque
import numpy as np

from ...core.logger import logger


class FrameAggregator:
    """帧聚合器 - 时间窗口聚合和关键帧选择"""
    
    def __init__(self, time_window: int = 10, max_frames: int = 5, 
                 min_frames: int = 1, aggregation_strategy: str = "uniform"):
        self.time_window = time_window  # 时间窗口（秒）
        self.max_frames = max_frames   # 每批最大帧数
        self.min_frames = min_frames   # 每批最小帧数
        self.aggregation_strategy = aggregation_strategy  # 聚合策略
        
        # 帧缓存
        self.frame_buffer = deque()
        self.last_output_time = None
        
        # 统计信息
        self.total_frames_received = 0
        self.total_batches_output = 0
        self.frames_aggregated = 0
        
        logger.info(f"帧聚合器初始化 - 时间窗口: {time_window}s, 最大帧数: {max_frames}")
    
    def add_frame(self, frame_data: Dict[str, Any]):
        """添加帧到聚合器"""
        try:
            self.total_frames_received += 1
            
            # 添加时间戳（如果没有）
            if 'timestamp' not in frame_data:
                frame_data['timestamp'] = datetime.utcnow()
            
            # 添加到缓冲区
            self.frame_buffer.append(frame_data)
            
            # 清理过期帧
            self._cleanup_expired_frames()
            
            logger.debug(f"添加帧到聚合器，当前缓存: {len(self.frame_buffer)} 帧")
            
        except Exception as e:
            logger.error(f"添加帧到聚合器失败: {e}")
    
    def should_output(self) -> bool:
        """判断是否应该输出聚合帧"""
        try:
            current_time = datetime.utcnow()
            
            # 检查是否有足够的帧
            if len(self.frame_buffer) < self.min_frames:
                return False
            
            # 检查时间条件
            if self.last_output_time is None:
                # 第一次输出
                return len(self.frame_buffer) >= self.max_frames
            
            # 检查时间窗口
            time_since_last_output = (current_time - self.last_output_time).total_seconds()
            
            # 时间窗口到期或达到最大帧数
            if (time_since_last_output >= self.time_window or 
                len(self.frame_buffer) >= self.max_frames):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"判断输出条件失败: {e}")
            return False
    
    def get_aggregated_frames(self) -> List[Dict[str, Any]]:
        """获取聚合的帧"""
        try:
            if not self.should_output():
                return []
            
            # 根据策略选择帧
            if self.aggregation_strategy == "uniform":
                selected_frames = self._select_uniform_frames()
            elif self.aggregation_strategy == "keyframe":
                selected_frames = self._select_key_frames()
            elif self.aggregation_strategy == "recent":
                selected_frames = self._select_recent_frames()
            else:
                selected_frames = self._select_uniform_frames()
            
            # 更新统计信息
            self.total_batches_output += 1
            self.frames_aggregated += len(selected_frames)
            self.last_output_time = datetime.utcnow()
            
            # 清空已处理的帧
            self.frame_buffer.clear()
            
            logger.debug(f"输出聚合帧批次: {len(selected_frames)} 帧")
            
            return selected_frames
            
        except Exception as e:
            logger.error(f"获取聚合帧失败: {e}")
            return []
    
    def _cleanup_expired_frames(self):
        """清理过期帧"""
        try:
            current_time = datetime.utcnow()
            cutoff_time = current_time - timedelta(seconds=self.time_window * 2)  # 保留2倍时间窗口
            
            while (self.frame_buffer and 
                   self.frame_buffer[0]['timestamp'] < cutoff_time):
                expired_frame = self.frame_buffer.popleft()
                logger.debug(f"清理过期帧: {expired_frame.get('frame_id', 'unknown')}")
                
        except Exception as e:
            logger.error(f"清理过期帧失败: {e}")
    
    def _select_uniform_frames(self) -> List[Dict[str, Any]]:
        """均匀选择帧"""
        try:
            frames = list(self.frame_buffer)
            frame_count = len(frames)
            
            if frame_count <= self.max_frames:
                return frames
            
            # 均匀采样
            indices = np.linspace(0, frame_count - 1, self.max_frames, dtype=int)
            selected_frames = [frames[i] for i in indices]
            
            logger.debug(f"均匀选择策略: 从 {frame_count} 帧中选择 {len(selected_frames)} 帧")
            
            return selected_frames
            
        except Exception as e:
            logger.error(f"均匀选择帧失败: {e}")
            return list(self.frame_buffer)[:self.max_frames]
    
    def _select_key_frames(self) -> List[Dict[str, Any]]:
        """选择关键帧（基于图像差异）"""
        try:
            frames = list(self.frame_buffer)
            if len(frames) <= self.max_frames:
                return frames
            
            # 简单的关键帧选择：基于时间间隔和图像变化
            selected_frames = [frames[0]]  # 第一帧
            
            for i in range(1, len(frames)):
                # 时间间隔检查
                time_diff = (frames[i]['timestamp'] - selected_frames[-1]['timestamp']).total_seconds()
                
                if time_diff >= (self.time_window / self.max_frames):
                    selected_frames.append(frames[i])
                    
                    if len(selected_frames) >= self.max_frames:
                        break
            
            # 如果还没有足够帧，添加最后一帧
            if len(selected_frames) < self.max_frames and frames[-1] not in selected_frames:
                selected_frames.append(frames[-1])
            
            logger.debug(f"关键帧选择策略: 从 {len(frames)} 帧中选择 {len(selected_frames)} 帧")
            
            return selected_frames
            
        except Exception as e:
            logger.error(f"关键帧选择失败: {e}")
            return self._select_uniform_frames()
    
    def _select_recent_frames(self) -> List[Dict[str, Any]]:
        """选择最近的帧"""
        try:
            frames = list(self.frame_buffer)
            
            # 按时间排序并选择最近的帧
            frames.sort(key=lambda x: x['timestamp'], reverse=True)
            selected_frames = frames[:self.max_frames]
            
            # 恢复时间顺序
            selected_frames.sort(key=lambda x: x['timestamp'])
            
            logger.debug(f"最近帧选择策略: 从 {len(frames)} 帧中选择 {len(selected_frames)} 帧")
            
            return selected_frames
            
        except Exception as e:
            logger.error(f"最近帧选择失败: {e}")
            return list(self.frame_buffer)[-self.max_frames:]
    
    def _calculate_frame_difference(self, frame1: Dict[str, Any], frame2: Dict[str, Any]) -> float:
        """计算两帧之间的差异"""
        try:
            # 简单的帧差异计算（这里可以优化）
            img1 = frame1.get('frame')
            img2 = frame2.get('frame')
            
            if img1 is None or img2 is None:
                return 1.0  # 默认最大差异
            
            # 转换为灰度图
            if len(img1.shape) == 3:
                gray1 = np.mean(img1, axis=2)
            else:
                gray1 = img1
                
            if len(img2.shape) == 3:
                gray2 = np.mean(img2, axis=2)
            else:
                gray2 = img2
            
            # 确保尺寸相同
            if gray1.shape != gray2.shape:
                return 1.0
            
            # 计算均方差
            diff = np.mean((gray1 - gray2) ** 2)
            normalized_diff = diff / (255 ** 2)  # 归一化到0-1
            
            return normalized_diff
            
        except Exception as e:
            logger.error(f"计算帧差异失败: {e}")
            return 1.0
    
    def set_strategy(self, strategy: str):
        """设置聚合策略"""
        valid_strategies = ["uniform", "keyframe", "recent"]
        if strategy in valid_strategies:
            self.aggregation_strategy = strategy
            logger.info(f"聚合策略设置为: {strategy}")
        else:
            logger.warning(f"无效的聚合策略: {strategy}, 有效选项: {valid_strategies}")
    
    def adjust_parameters(self, time_window: int = None, max_frames: int = None, 
                         min_frames: int = None):
        """调整聚合参数"""
        if time_window is not None:
            self.time_window = time_window
            logger.info(f"时间窗口调整为: {time_window}s")
        
        if max_frames is not None:
            self.max_frames = max_frames
            logger.info(f"最大帧数调整为: {max_frames}")
        
        if min_frames is not None:
            self.min_frames = min_frames
            logger.info(f"最小帧数调整为: {min_frames}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取聚合器统计信息"""
        return {
            "total_frames_received": self.total_frames_received,
            "total_batches_output": self.total_batches_output,
            "frames_aggregated": self.frames_aggregated,
            "current_buffer_size": len(self.frame_buffer),
            "avg_frames_per_batch": round(self.frames_aggregated / max(self.total_batches_output, 1), 2),
            "aggregation_strategy": self.aggregation_strategy,
            "time_window": self.time_window,
            "max_frames": self.max_frames,
            "min_frames": self.min_frames,
            "last_output_time": self.last_output_time.isoformat() if self.last_output_time else None
        }
    
    def clear_buffer(self):
        """清空帧缓冲区"""
        try:
            self.frame_buffer.clear()
            logger.info("帧聚合器缓冲区已清空")
            
        except Exception as e:
            logger.error(f"清空聚合器缓冲区失败: {e}")
    
    def get_buffer_status(self) -> Dict[str, Any]:
        """获取缓冲区状态"""
        try:
            if not self.frame_buffer:
                return {"status": "empty", "frames": 0}
            
            oldest_frame_time = self.frame_buffer[0]['timestamp']
            newest_frame_time = self.frame_buffer[-1]['timestamp']
            time_span = (newest_frame_time - oldest_frame_time).total_seconds()
            
            return {
                "status": "active",
                "frames": len(self.frame_buffer),
                "time_span": round(time_span, 2),
                "oldest_frame": oldest_frame_time.isoformat(),
                "newest_frame": newest_frame_time.isoformat(),
                "ready_for_output": self.should_output()
            }
            
        except Exception as e:
            logger.error(f"获取缓冲区状态失败: {e}")
            return {"status": "error", "error": str(e)} 