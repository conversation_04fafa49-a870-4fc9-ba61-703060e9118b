"""
API路由初始化
"""
from fastapi import APIRouter

from .routes import alerts, health, cameras, websocket

# 创建主API路由
api_router = APIRouter(prefix="/api/v1")

# 注册各个模块的路由
api_router.include_router(health.router)
api_router.include_router(alerts.router)
api_router.include_router(cameras.router)

# WebSocket路由需要单独处理，不包含在API前缀中
websocket_router = APIRouter()
websocket_router.include_router(websocket.router)

__all__ = ["api_router", "websocket_router"] 