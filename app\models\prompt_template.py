"""
提示词模板数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, ForeignKey,
    Enum as SQLEnum, JSON
)
from sqlalchemy.orm import relationship
from enum import Enum
from .base import BaseModel


class TemplateType(Enum):
    """模板类型枚举"""
    GLOBAL = "global"
    BEHAVIOR = "behavior"
    ALGORITHM_BEHAVIOR = "algorithm_behavior"


class PromptTemplate(BaseModel):
    """提示词模板模型"""
    
    __tablename__ = "prompt_templates"
    
    # 基本信息
    template_name = Column(String(100), nullable=False, index=True, comment="模板名称")
    template_code = Column(String(50), unique=True, nullable=False, index=True, comment="模板编码")
    template_type = Column(
        SQLEnum(TemplateType), 
        nullable=False, 
        index=True, 
        comment="模板类型"
    )
    description = Column(Text, comment="模板描述")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 关联信息
    anomaly_behavior_id = Column(
        Integer, 
        ForeignKey("anomaly_behaviors.id"), 
        nullable=True, 
        index=True, 
        comment="关联异常行为ID"
    )
    
    # 模板内容
    prompt_content = Column(Text, nullable=False, comment="提示词内容")
    variables = Column(JSON, comment="模板变量定义")
    
    # 优先级配置
    priority = Column(Integer, default=100, comment="优先级(数值越小优先级越高)")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    
    # 关联关系
    anomaly_behavior = relationship("AnomalyBehavior", back_populates="prompt_templates")
    
    def __repr__(self) -> str:
        return f"<PromptTemplate(id={self.id}, name={self.template_name}, type={self.template_type.value})>"
    
    @property
    def is_global_template(self) -> bool:
        """是否为全局模板"""
        return self.template_type == TemplateType.GLOBAL
    
    @property
    def is_behavior_template(self) -> bool:
        """是否为行为默认模板"""
        return self.template_type == TemplateType.BEHAVIOR
    
    @property
    def is_algorithm_behavior_template(self) -> bool:
        """是否为算法行为专用模板"""
        return self.template_type == TemplateType.ALGORITHM_BEHAVIOR
    
    def get_variable_definition(self, var_name: str) -> dict:
        """获取变量定义"""
        if not self.variables:
            return {}
        return self.variables.get(var_name, {})
    
    def get_all_variables(self) -> list:
        """获取所有变量名"""
        if not self.variables:
            return []
        return list(self.variables.keys())
    
    def render_prompt(self, context: dict = None) -> str:
        """渲染提示词模板"""
        if not context:
            return self.prompt_content
        
        content = self.prompt_content
        for var_name, value in context.items():
            placeholder = f"{{{var_name}}}"
            content = content.replace(placeholder, str(value))
        
        return content
    
    def validate_variables(self, context: dict) -> list:
        """验证变量是否完整"""
        missing_vars = []
        if not self.variables:
            return missing_vars
        
        for var_name, var_config in self.variables.items():
            required = var_config.get("required", False)
            if required and var_name not in context:
                missing_vars.append(var_name)
        
        return missing_vars
    
    def increment_usage(self) -> None:  
        """增加使用次数"""
        self.usage_count = (self.usage_count or 0) + 1 