"""
帧调度器 - AI分析任务调度核心
负责管理AI分析任务的队列和分发
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import time
from collections import deque

from ...core.logger import logger
from ...core.exceptions import SchedulerError


class FrameScheduler:
    """帧调度器 - AI分析任务调度"""
    
    def __init__(self, max_queue_size: int = 1000, max_concurrent_tasks: int = 10):
        self.max_queue_size = max_queue_size
        self.max_concurrent_tasks = max_concurrent_tasks
        
        # 任务队列
        self.task_queue = asyncio.Queue(maxsize=max_queue_size)
        self.processing_tasks = {}  # task_id -> task_info
        
        # 调度状态
        self.is_running = False
        self.scheduler_tasks = []
        
        # AI分析回调
        self.ai_analyzer_callback: Optional[Callable] = None
        
        # 统计信息
        self.total_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.queue_full_drops = 0
        
        # 性能监控
        self.avg_processing_time = 0
        self.processing_times = deque(maxlen=100)  # 保留最近100个处理时间
        
        logger.info("帧调度器初始化完成")
    
    async def start(self):
        """启动帧调度器"""
        if self.is_running:
            logger.warning("帧调度器已在运行")
            return
        
        try:
            self.is_running = True
            
            # 启动调度器工作任务
            for i in range(self.max_concurrent_tasks):
                task = asyncio.create_task(self._scheduler_worker(f"worker-{i}"))
                self.scheduler_tasks.append(task)
            
            logger.info(f"帧调度器启动成功，启动 {self.max_concurrent_tasks} 个工作任务")
            
        except Exception as e:
            logger.error(f"帧调度器启动失败: {e}")
            await self.stop()
            raise SchedulerError(f"帧调度器启动失败: {e}")
    
    async def stop(self):
        """停止帧调度器"""
        if not self.is_running:
            return
        
        try:
            self.is_running = False
            
            # 停止所有工作任务
            for task in self.scheduler_tasks:
                task.cancel()
            
            # 等待任务完成
            if self.scheduler_tasks:
                await asyncio.gather(*self.scheduler_tasks, return_exceptions=True)
            
            self.scheduler_tasks.clear()
            
            logger.info("帧调度器已停止")
            
        except Exception as e:
            logger.error(f"停止帧调度器时出错: {e}")
    
    def set_ai_analyzer_callback(self, callback: Callable):
        """设置AI分析器回调函数"""
        self.ai_analyzer_callback = callback
        logger.info("AI分析器回调函数已设置")
    
    async def submit_analysis_task(self, task_data: Dict[str, Any]) -> bool:
        """提交AI分析任务"""
        try:
            task_id = task_data.get("task_id")
            
            # 检查队列是否已满
            if self.task_queue.full():
                self.queue_full_drops += 1
                logger.warning(f"任务队列已满，丢弃任务: {task_id}")
                return False
            
            # 添加任务元数据
            task_data.update({
                "submit_time": datetime.utcnow(),
                "status": "queued"
            })
            
            # 提交到队列
            await self.task_queue.put(task_data)
            self.total_tasks += 1
            
            logger.debug(f"任务 {task_id} 已提交到调度队列")
            return True
            
        except Exception as e:
            logger.error(f"提交分析任务失败: {e}")
            return False
    
    async def _scheduler_worker(self, worker_name: str):
        """调度器工作任务"""
        logger.info(f"调度器工作任务 {worker_name} 启动")
        
        while self.is_running:
            try:
                # 从队列获取任务
                task_data = await asyncio.wait_for(
                    self.task_queue.get(), timeout=1.0
                )
                
                if not task_data:
                    continue
                
                task_id = task_data.get("task_id")
                start_time = time.time()
                
                # 更新任务状态
                task_data["status"] = "processing"
                task_data["start_time"] = datetime.utcnow()
                task_data["worker"] = worker_name
                self.processing_tasks[task_id] = task_data
                
                logger.debug(f"工作任务 {worker_name} 开始处理任务: {task_id}")
                
                # 执行AI分析
                if self.ai_analyzer_callback:
                    try:
                        result = await self.ai_analyzer_callback(task_data)
                        
                        # 处理成功
                        end_time = time.time()
                        processing_time = end_time - start_time
                        
                        self._update_processing_time(processing_time)
                        self.completed_tasks += 1
                        
                        logger.debug(f"任务 {task_id} 处理完成，耗时: {processing_time:.2f}秒")
                        
                    except Exception as e:
                        # 处理失败
                        self.failed_tasks += 1
                        logger.error(f"任务 {task_id} 处理失败: {e}")
                        
                        # 根据错误类型决定是否重试
                        if self._should_retry_task(task_data, e):
                            await self._retry_task(task_data)
                
                else:
                    logger.warning("AI分析器回调未设置")
                
                # 清理任务记录
                if task_id in self.processing_tasks:
                    del self.processing_tasks[task_id]
                
                # 标记任务完成
                self.task_queue.task_done()
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"调度器工作任务 {worker_name} 异常: {e}")
                await asyncio.sleep(1)
        
        logger.info(f"调度器工作任务 {worker_name} 已停止")
    
    def _update_processing_time(self, processing_time: float):
        """更新处理时间统计"""
        self.processing_times.append(processing_time)
        if self.processing_times:
            self.avg_processing_time = sum(self.processing_times) / len(self.processing_times)
    
    def _should_retry_task(self, task_data: Dict[str, Any], error: Exception) -> bool:
        """判断任务是否应该重试"""
        # 获取重试次数
        retry_count = task_data.get("retry_count", 0)
        max_retries = 2  # 最大重试2次
        
        # 检查重试次数
        if retry_count >= max_retries:
            return False
        
        # 检查错误类型（某些错误不重试）
        error_type = type(error).__name__
        non_retryable_errors = ["ValidationError", "AuthenticationError"]
        
        if error_type in non_retryable_errors:
            return False
        
        return True
    
    async def _retry_task(self, task_data: Dict[str, Any]):
        """重试任务"""
        try:
            task_id = task_data.get("task_id")
            retry_count = task_data.get("retry_count", 0) + 1
            
            # 更新重试信息
            task_data["retry_count"] = retry_count
            task_data["status"] = "retry"
            task_data["last_retry_time"] = datetime.utcnow()
            
            # 延迟后重新提交
            await asyncio.sleep(min(retry_count * 2, 10))  # 指数退避，最大10秒
            
            await self.task_queue.put(task_data)
            logger.info(f"任务 {task_id} 第 {retry_count} 次重试")
            
        except Exception as e:
            logger.error(f"重试任务失败: {e}")
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.task_queue.qsize()
    
    def get_processing_count(self) -> int:
        """获取正在处理的任务数"""
        return len(self.processing_tasks)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        return {
            "is_running": self.is_running,
            "queue_size": self.get_queue_size(),
            "processing_count": self.get_processing_count(),
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "queue_full_drops": self.queue_full_drops,
            "avg_processing_time": round(self.avg_processing_time, 2),
            "success_rate": round(self.completed_tasks / max(self.total_tasks, 1) * 100, 2),
            "active_workers": len([t for t in self.scheduler_tasks if not t.done()])
        }
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.processing_tasks.get(task_id)
    
    def get_processing_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有正在处理的任务"""
        return self.processing_tasks.copy()
    
    async def clear_queue(self):
        """清空任务队列"""
        try:
            while not self.task_queue.empty():
                try:
                    self.task_queue.get_nowait()
                    self.task_queue.task_done()
                except asyncio.QueueEmpty:
                    break
            
            logger.info("任务队列已清空")
            
        except Exception as e:
            logger.error(f"清空任务队列失败: {e}")
    
    async def pause_processing(self):
        """暂停处理（停止接收新任务）"""
        # 可以添加暂停标志
        pass
    
    async def resume_processing(self):
        """恢复处理"""
        # 可以添加恢复逻辑
        pass 