"""
证据文件数据模型
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime,
    ForeignKey, BigInteger, Enum as SQLEnum
)
from sqlalchemy.orm import relationship
from enum import Enum
from .base import BaseModel


class FileType(Enum):
    """文件类型枚举"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    OTHER = "other"


class EvidenceFile(BaseModel):
    """证据文件模型"""
    
    __tablename__ = "evidence_files"
    
    # 关联信息
    alert_event_id = Column(Integer, ForeignKey("alert_events.id"), nullable=False, index=True, comment="关联预警事件ID")
    camera_id = Column(Integer, ForeignKey("cameras.id"), nullable=False, index=True, comment="摄像头ID")
    
    # 文件基本信息
    file_name = Column(String(255), nullable=False, comment="文件名")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    file_url = Column(String(500), comment="文件访问URL")
    file_type = Column(SQLEnum(FileType), nullable=False, comment="文件类型")
    file_size = Column(BigInteger, comment="文件大小(字节)")
    mime_type = Column(String(100), comment="MIME类型")
    
    # 文件元数据
    duration = Column(Integer, comment="时长(秒,视频/音频)")
    width = Column(Integer, comment="宽度(像素,图片/视频)")
    height = Column(Integer, comment="高度(像素,图片/视频)")
    fps = Column(Integer, comment="帧率(视频)")
    
    # 存储信息
    storage_type = Column(String(20), default="minio", comment="存储类型")
    bucket_name = Column(String(100), comment="存储桶名称")
    object_key = Column(String(500), comment="对象键")
    
    # 文件状态
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    is_deleted = Column(Boolean, default=False, comment="是否已删除")
    delete_reason = Column(String(200), comment="删除原因")
    
    # 时间信息
    captured_at = Column(DateTime, nullable=False, comment="拍摄时间")
    uploaded_at = Column(DateTime, comment="上传时间")
    
    # 关联关系
    alert_event = relationship("AlertEvent", back_populates="evidence_files")
    camera = relationship("Camera", back_populates="evidence_files")
    
    def __repr__(self) -> str:
        return f"<EvidenceFile(id={self.id}, name={self.file_name}, type={self.file_type.value})>"
    
    @property
    def is_image(self) -> bool:
        """是否为图片文件"""
        return self.file_type == FileType.IMAGE
    
    @property
    def is_video(self) -> bool:
        """是否为视频文件"""
        return self.file_type == FileType.VIDEO
    
    @property
    def is_audio(self) -> bool:
        """是否为音频文件"""
        return self.file_type == FileType.AUDIO
    
    @property
    def file_size_mb(self) -> float:
        """文件大小(MB)"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0.0
    
    @property
    def file_extension(self) -> str:
        """文件扩展名"""
        if self.file_name and "." in self.file_name:
            return self.file_name.split(".")[-1].lower()
        return ""
    
    @property
    def resolution(self) -> str:
        """分辨率字符串"""
        if self.width and self.height:
            return f"{self.width}x{self.height}"
        return ""
    
    def get_full_url(self, base_url: str) -> str:
        """获取完整访问URL"""
        if self.file_url:
            if self.file_url.startswith("http"):
                return self.file_url
            else:
                return f"{base_url.rstrip('/')}/{self.file_url.lstrip('/')}"
        return ""
    
    def mark_as_processed(self) -> None:
        """标记为已处理"""
        self.is_processed = True
    
    def mark_as_deleted(self, reason: str = None) -> None:
        """标记为已删除"""
        self.is_deleted = True
        if reason:
            self.delete_reason = reason
    
    def set_media_info(self, width: int = None, height: int = None, 
                      duration: int = None, fps: int = None) -> None:
        """设置媒体信息"""
        if width:
            self.width = width
        if height:
            self.height = height
        if duration:
            self.duration = duration
        if fps:
            self.fps = fps 