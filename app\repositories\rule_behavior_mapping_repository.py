"""
规则异常行为关联数据访问层
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..database.base_repository import BaseRepository
from ..models.rule_behavior_mapping import RuleBehaviorMapping
from ..core.logger import logger


class RuleBehaviorMappingRepository(BaseRepository[RuleBehaviorMapping]):
    """规则行为映射Repository"""
    
    def __init__(self, db: Session):
        super().__init__(db, RuleBehaviorMapping)
    
    async def get_rule_mappings(self, rule_id: int) -> List[RuleBehaviorMapping]:
        """获取规则的所有行为映射"""
        return await self.find_by_conditions([
            RuleBehaviorMapping.rule_id == rule_id,
            RuleBehaviorMapping.is_enabled == True,
            RuleBehaviorMapping.deleted == False
        ])
    
    async def get_behavior_mappings(self, behavior_id: int) -> List[RuleBehaviorMapping]:
        """获取异常行为的所有规则映射"""
        return await self.find_by_conditions([
            RuleBehaviorMapping.behavior_id == behavior_id,
            RuleBehaviorMapping.is_enabled == True,
            RuleBehaviorMapping.deleted == False
        ])
    
    async def get_mapping(self, rule_id: int, behavior_id: int) -> Optional[RuleBehaviorMapping]:
        """获取特定的规则-行为映射"""
        return await self.find_by_conditions([
            RuleBehaviorMapping.rule_id == rule_id,
            RuleBehaviorMapping.behavior_id == behavior_id,
            RuleBehaviorMapping.deleted == False
        ], single=True)
    
    async def batch_create_mappings(self, mappings: List[Dict[str, Any]]) -> int:
        """批量创建映射"""
        try:
            created_count = 0
            for mapping in mappings:
                mapping_id = await self.create(mapping)
                if mapping_id:
                    created_count += 1
            
            logger.info(f"批量创建映射，成功创建 {created_count} 个映射")
            return created_count
            
        except Exception as e:
            logger.error(f"批量创建映射失败: {e}")
            return 0 